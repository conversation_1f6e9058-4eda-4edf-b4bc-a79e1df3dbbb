
import React from 'react';
import { GraduationCap, Users, Heart, Briefcase, Globe, Newspaper, Car, Facebook, Twitter, Instagram } from 'lucide-react';
import { getCategoryColor, getCategoryColorHex } from './utils';
import VolunteerDonateSection from './VolunteerDonateSection';
import NewsletterSection from './NewsletterSection';
import { Link } from 'react-router-dom';

const programs = [
  {
    title: 'DRIVING LESSON',
    slug: 'driving-lesson',
    category: 'Education',
    image: '/US-College-Life-101-Program-2.jpg',
    description: 'Helping refugees and immigrants gain independence and mobility through professional driving lessons.',
    icon: Car
  },
  {
    title: 'Academic Improvement Program',
    slug: 'academic-improvement-program',
    category: 'Education',
    image: '/IMG_6267-scaled (1).jpg',
    description: 'Tutoring and academic support for children and adults to help them succeed in school.',
    icon: GraduationCap
  },
  {
    title: 'Refugee Women Empowerment Program',
    slug: 'refugee-women-empowerment-program',
    category: 'Empowerment',
    image: '/IMG_9829 (1).jpg',
    description: 'Supporting refugee women with leadership, skills training, and community-building opportunities.',
    icon: Heart
  },
  {
    title: 'Sewing Machine Program',
    slug: 'sewing-machine-program',
    category: 'Skills',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    description: 'Providing sewing machines and training to foster self-reliance and entrepreneurship.',
    icon: Briefcase
  },
  {
    title: 'School Transportation Program',
    slug: 'school-transportation-program',
    category: 'Support',
    image: '/US-College-Life-101-Program-2.jpg',
    description: 'Ensuring safe and reliable transportation for students to and from school.',
    icon: Car
  },
  {
    title: 'Refugee and Immigrant Newspaper',
    slug: 'refugee-immigrant-newspaper',
    category: 'Media',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    description: 'A community newspaper sharing stories, news, and resources for refugees and immigrants.',
    icon: Newspaper
  },
  {
    title: 'Refugee and Immigrant Voice Group',
    slug: 'refugee-immigrant-voice-group',
    category: 'Community',
    image: '/IMG_9829 (1).jpg',
    description: 'A platform for refugees and immigrants to share their voices and advocate for their needs.',
    icon: Users
  }
];

const Programs = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section
        className="relative flex flex-col items-center justify-center min-h-[400px] py-20 w-full"
        style={{
          backgroundImage: 'url(/US-College-Life-101-Program-2.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          paddingTop: '80px',
          paddingBottom: '80px',
        }}
      >
        {/* Overlay div on top of image */}
        <div style={{ position: 'absolute', inset: 0, background: 'rgba(0,0,0,0.4)' }}></div>
        <div className="relative z-10 flex flex-col items-center justify-center w-full">
          <h1
            className="font-bold mb-6 text-center"
            style={{ color: '#fff', textTransform: 'capitalize', letterSpacing: 'normal', fontSize: '60px' }}
          >
            Our <span style={{ color: '#FFC107' }}>Programs</span>
          </h1>
          <p
            className="text-center"
            style={{
              color: '#fff',
              fontSize: '24px',
              lineHeight: 1.6,
              maxWidth: '700px',
              margin: '0 auto',
            }}
          >
            Empowering refugees and immigrants through comprehensive support programs designed to build skills, foster community, and create opportunities for success.
          </p>
        </div>
      </section>

      {/* Programs Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-center">
          {programs.map((program, index) => {
            const IconComponent = program.icon;
            return (
              <div 
                key={index}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group animate-fade-in w-full max-w-sm justify-self-center"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Image Container */}
                <div className="relative h-64 overflow-hidden">
                  <img 
                    src={program.image} 
                    alt={program.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span
                      className="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium text-white"
                      style={{ backgroundColor: getCategoryColorHex(program.category) }}
                    >
                      <IconComponent className="w-5 h-5 mr-2" />
                      {program.category}
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-deep-blue transition-colors">
                    {program.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    {program.description}
                  </p>

                  {/* Learn More Button */}
                  <Link 
                    to={`/program/${program.slug}`}
                    className="inline-flex items-center px-6 py-3 bg-deep-blue text-white font-medium rounded-lg hover:bg-bright-blue transition-colors duration-200 group-hover:shadow-md"
                  >
                    Learn More
                    <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Want to Make a Difference Section */}
      <VolunteerDonateSection />

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Programs;
