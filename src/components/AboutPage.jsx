import React from 'react';
import { Heart, Users, Target, HandHeart, Award, Globe, Calendar, ArrowRight, GraduationCap, Briefcase, Home } from 'lucide-react';

function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-deep-blue to-bright-blue text-white overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="/RIA-Recognition-Dinner-Pic-4.jpg"
            alt="Community recognition dinner"
            className="w-full h-full object-cover opacity-20"
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              About <span className="text-yellow-400">HopeForward</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed opacity-90">
              For over 15 years, we've been dedicated to empowering refugee families through comprehensive support services that transform lives and build stronger communities.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our <span className="text-deep-blue">Mission</span>
              </h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                HopeForward exists to empower refugee families by providing comprehensive, culturally responsive support services that address their unique challenges and unlock their potential for success in their new communities.
              </p>
              <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                We believe that every refugee family deserves the opportunity to thrive, not just survive. Through our integrated approach to education, workforce development, and community integration, we help families build stable, prosperous lives while maintaining their cultural identity and values.
              </p>
              <div className="flex items-center bg-deep-blue text-white px-6 py-4 rounded-lg">
                <Target className="h-6 w-6 mr-3" />
                <span className="font-semibold">Empowering 500+ families since 2009</span>
              </div>
            </div>
            <div className="relative">
              <img
                src="/IMG_6267-scaled (1).jpg"
                alt="Community children celebrating"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-yellow-500 text-white p-6 rounded-lg shadow-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold">15+</div>
                  <div className="text-sm">Years of Service</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="order-2 lg:order-1 relative">
              <img
                src="/US-College-Life-101-Program-2.jpg"
                alt="College Life Program participants"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -top-6 -left-6 bg-bright-blue text-white p-6 rounded-lg shadow-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold">85%</div>
                  <div className="text-sm">Success Rate</div>
                </div>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our <span className="text-bright-blue">Vision</span>
              </h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                We envision communities where refugee families are not just welcomed, but celebrated for their resilience, diversity, and contributions. A future where every refugee has the tools, support, and opportunities needed to achieve their dreams.
              </p>
              <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                Through our work, we're building bridges between cultures, fostering understanding, and creating a more inclusive society where everyone can thrive regardless of their origin story.
              </p>
              <div className="flex items-center bg-bright-blue text-white px-6 py-4 rounded-lg">
                <Globe className="h-6 w-6 mr-3" />
                <span className="font-semibold">Building inclusive communities together</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Approach */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our <span className="text-deep-blue">Approach</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe in a holistic, culturally responsive approach that addresses the whole person and family.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-deep-blue rounded-full flex items-center justify-center mb-6">
                <Heart className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Culturally Responsive</h3>
              <p className="text-gray-700 leading-relaxed">
                We honor and celebrate the diverse cultural backgrounds of the families we serve, integrating cultural sensitivity into every program and service.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-bright-blue rounded-full flex items-center justify-center mb-6">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Family-Centered</h3>
              <p className="text-gray-700 leading-relaxed">
                Our programs are designed to support entire families, recognizing that individual success is deeply connected to family stability and wellbeing.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-6">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Results-Driven</h3>
              <p className="text-gray-700 leading-relaxed">
                We measure our success through the achievements of the families we serve, continuously improving our programs based on outcomes and feedback.
              </p>
            </div>
          </div>

          <div className="bg-gradient-to-r from-deep-blue to-bright-blue rounded-2xl p-8 md:p-12 text-white">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl md:text-3xl font-bold mb-6">
                  Community Partnership Model
                </h3>
                <p className="text-lg text-blue-100 mb-6 leading-relaxed">
                  We work closely with local organizations, schools, employers, and community leaders to create a comprehensive support network that extends far beyond our direct services.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <ArrowRight className="h-5 w-5 text-yellow-400 mr-3" />
                    <span>Collaborative program design</span>
                  </div>
                  <div className="flex items-center">
                    <ArrowRight className="h-5 w-5 text-yellow-400 mr-3" />
                    <span>Shared resource development</span>
                  </div>
                  <div className="flex items-center">
                    <ArrowRight className="h-5 w-5 text-yellow-400 mr-3" />
                    <span>Community advocacy initiatives</span>
                  </div>
                </div>
              </div>
              <div>
                <img
                  src="/IMG_9829 (1).jpg"
                  alt="Women's empowerment program participants"
                  className="rounded-lg shadow-xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our <span className="text-deep-blue">Leadership</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Meet the dedicated team leading HopeForward's mission to empower refugee families.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-deep-blue to-bright-blue rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-3xl font-bold text-white">SJ</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Sarah Johnson</h3>
              <p className="text-deep-blue font-medium mb-3">Executive Director</p>
              <p className="text-gray-600 text-sm leading-relaxed">
                With over 12 years in refugee services, Sarah brings passion and expertise to leading HopeForward's strategic vision and program development.
              </p>
            </div>

            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-bright-blue to-yellow-500 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-3xl font-bold text-white">AM</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Ahmed Mohamed</h3>
              <p className="text-bright-blue font-medium mb-3">Program Director</p>
              <p className="text-gray-600 text-sm leading-relaxed">
                A former refugee himself, Ahmed understands firsthand the challenges families face and leads our direct service programs with empathy and insight.
              </p>
            </div>

            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-yellow-500 to-deep-blue rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-3xl font-bold text-white">MR</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Maria Rodriguez</h3>
              <p className="text-yellow-600 font-medium mb-3">Community Outreach Manager</p>
              <p className="text-gray-600 text-sm leading-relaxed">
                Maria builds bridges between HopeForward and the broader community, fostering partnerships and advocating for refugee rights and inclusion.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-bright-blue to-deep-blue text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Make a <span className="text-yellow-400">Difference?</span>
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Join us in empowering refugee families and building stronger, more inclusive communities. Every contribution makes a meaningful impact.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="bg-yellow-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-yellow-600 transition-all transform hover:scale-105 shadow-lg">
              Get Involved Today
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-deep-blue transition-all">
              Learn About Our Programs
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}

export default AboutPage;