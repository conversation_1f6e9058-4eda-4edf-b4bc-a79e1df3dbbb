
import React, { useState } from 'react';
import { X, Heart, CreditCard, Smartphone, DollarSign } from 'lucide-react';
import { useDonationModal } from '../contexts/DonationModalContext';

const DonationModal = () => {
  const { isModalOpen, closeModal } = useDonationModal();
  const [donationAmount, setDonationAmount] = useState('');
  const [customAmount, setCustomAmount] = useState('');
  const [selectedPayment, setSelectedPayment] = useState('');
  const [donationType, setDonationType] = useState('one-time');

  const predefinedAmounts = [10, 20, 50, 100, 250, 500];

  const paymentMethods = [
    { id: 'paypal', name: 'PayPal', icon: '💙', color: 'bg-blue-600' },
    { id: 'stripe', name: 'Credit/Debit Card', icon: <CreditCard className="h-5 w-5" />, color: 'bg-purple-600' },
    { id: 'cashapp', name: 'Cash App', icon: '💚', color: 'bg-green-600' },
    { id: 'venmo', name: '<PERSON>en<PERSON>', icon: '🔵', color: 'bg-blue-500' },
    { id: 'zelle', name: 'Zelle', icon: '💜', color: 'bg-purple-500' },
    { id: 'applepay', name: 'Apple Pay', icon: '🍎', color: 'bg-gray-800' }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const amount = customAmount || donationAmount;
    
    if (!amount || !selectedPayment) {
      alert('Please select an amount and payment method');
      return;
    }

    // For demo purposes, we'll show an alert
    // In a real implementation, you would integrate with actual payment processors
    alert(`Thank you for your ${donationType} donation of $${amount} via ${paymentMethods.find(p => p.id === selectedPayment)?.name}!\n\nThis is a demo - actual payment processing would be implemented with real payment gateways.`);
    
    // Reset form
    setDonationAmount('');
    setCustomAmount('');
    setSelectedPayment('');
    setDonationType('one-time');
    closeModal();
  };

  if (!isModalOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={closeModal}
        ></div>

        {/* Modal */}
        <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Support Our Mission</h2>
                <p className="text-gray-600">Every donation helps refugee families build new lives</p>
              </div>
            </div>
            <button
              onClick={closeModal}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Donation Type */}
            <div>
              <label className="block text-sm font-semibold text-gray-900 mb-3">
                Donation Type
              </label>
              <div className="grid grid-cols-2 gap-3">
                <label className="flex items-center p-3 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="donationType"
                    value="one-time"
                    checked={donationType === 'one-time'}
                    onChange={(e) => setDonationType(e.target.value)}
                    className="h-4 w-4 text-blue-600"
                  />
                  <div className="ml-3">
                    <div className="font-medium text-gray-900">One-time</div>
                    <div className="text-sm text-gray-500">Single donation</div>
                  </div>
                </label>

                <label className="flex items-center p-3 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="donationType"
                    value="monthly"
                    checked={donationType === 'monthly'}
                    onChange={(e) => setDonationType(e.target.value)}
                    className="h-4 w-4 text-blue-600"
                  />
                  <div className="ml-3">
                    <div className="font-medium text-gray-900">Monthly</div>
                    <div className="text-sm text-gray-500">Recurring donation</div>
                  </div>
                </label>
              </div>
            </div>

            {/* Amount Selection */}
            <div>
              <label className="block text-sm font-semibold text-gray-900 mb-3">
                Donation Amount
              </label>
              <div className="grid grid-cols-3 gap-3 mb-4">
                {predefinedAmounts.map((amount) => (
                  <button
                    key={amount}
                    type="button"
                    onClick={() => {
                      setDonationAmount(amount);
                      setCustomAmount('');
                    }}
                    className={`p-3 border-2 rounded-lg font-semibold transition-colors ${
                      donationAmount === amount && !customAmount
                        ? 'border-blue-600 bg-blue-50 text-blue-600'
                        : 'border-gray-300 hover:border-blue-300 text-gray-700'
                    }`}
                  >
                    ${amount}
                  </button>
                ))}
              </div>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  placeholder="Enter custom amount"
                  value={customAmount}
                  onChange={(e) => {
                    setCustomAmount(e.target.value);
                    setDonationAmount('');
                  }}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="1"
                />
              </div>
            </div>

            {/* Payment Methods */}
            <div>
              <label className="block text-sm font-semibold text-gray-900 mb-3">
                Payment Method
              </label>
              <div className="grid grid-cols-2 gap-3">
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${
                      selectedPayment === method.id
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300 hover:border-blue-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value={method.id}
                      checked={selectedPayment === method.id}
                      onChange={(e) => setSelectedPayment(e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3 flex items-center space-x-2">
                      <span className="text-lg">
                        {typeof method.icon === 'string' ? method.icon : method.icon}
                      </span>
                      <span className="font-medium text-gray-900">{method.name}</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Impact Message */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Your Impact</h4>
              <p className="text-blue-800 text-sm">
                {customAmount || donationAmount ? (
                  <>Your ${customAmount || donationAmount} donation can help provide essential services like English classes, job training, and community support for refugee families.</>
                ) : (
                  'Select an amount to see how your donation will make a difference.'
                )}
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={closeModal}
                className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!donationAmount && !customAmount || !selectedPayment}
                className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <Heart className="h-5 w-5 mr-2" />
                Donate {donationType === 'monthly' ? 'Monthly' : 'Now'}
                {(donationAmount || customAmount) && (
                  <span className="ml-2">
                    ${customAmount || donationAmount}
                  </span>
                )}
              </button>
            </div>

            <div className="text-center text-xs text-gray-500">
              <p>🔒 This is a demonstration. Actual payment processing would be integrated with real payment gateways.</p>
              <p className="mt-1">All donations are secure and go directly to supporting refugee families.</p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DonationModal;
