import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, Target, Globe, Heart, Users, ArrowRight, HandHeart, ShieldCheck, Home, Sparkles } from 'lucide-react';
import VolunteerDonateSection from './VolunteerDonateSection';
import NewsletterSection from './NewsletterSection';
import { Facebook, Twitter, Instagram } from 'lucide-react';

export default function WhoWeAre() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = useState(false);
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleAboutDropdown = () => setIsAboutDropdownOpen((v) => !v);
  const closeAboutDropdown = () => setIsAboutDropdownOpen(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[60vh] min-h-[400px] flex items-center justify-center overflow-hidden">
        <img
          src="/RIA-Recognition-Dinner-Pic-4.jpg"
          alt="Community recognition dinner"
          className="absolute inset-0 w-full h-full object-cover z-0"
          style={{ filter: 'brightness(1)' }}
        />
        {/* No overlay, text is clear over image, add drop shadow for readability */}
        <div className="relative z-20 max-w-3xl mx-auto text-center px-4">
          <h1 className="text-3xl md:text-5xl font-bold mb-6 text-yellow-400 drop-shadow-lg" style={{textShadow: '0 2px 8px rgba(0,0,0,0.35)'}}>Support is just a phone call away</h1>
          <p className="text-lg md:text-2xl text-white font-medium drop-shadow-lg mx-auto max-w-2xl" style={{textShadow: '0 2px 8px rgba(0,0,0,0.35)'}}>
            Our team is made up of people from all walks of life including those whom have never lived outside of Iowa to those who have been born in far-reaching places of the world. We come together to provide a welcoming atmosphere to our community.
          </p>
        </div>
      </section>

      {/* Mission & Vision Section (new design) */}
      <section className="w-full bg-[#f9f9f9] py-20 px-4 md:px-8" style={{fontFamily: 'Inter, sans-serif'}}>
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-12 items-start">
          {/* Mission Content */}
          <div className="flex flex-col justify-start order-1">
            <h2 className="font-bold mb-4" style={{color: '#25556B', fontSize: '36px'}}>Who We Are</h2>
            <p className="text-[#374151] mb-4" style={{fontSize: '18px'}}>In 2013, a group of former refugees came together to formally continue the support they had been offering individually, by guiding newly arrived refugees in Iowa with transportation, essentials, and interpretation. United by a mission to preserve culture and empower others, they committed to helping fellow refugees thrive in a new environment.</p>
            <p className="font-semibold mt-4" style={{ color: '#FACC15', fontSize: '18px' }}>
              Formerly known as the CongoReform Association, the Refugee and Immigrant Association gained 501(c)(3) nonprofit status in 2015.
            </p>
            <div className="inline-flex items-center mt-4 rounded-md" style={{backgroundColor: '#1e3a8a', color: '#fff', padding: '0.75rem 1.25rem'}}>
              <Target className="h-5 w-5 mr-2" />
              <span className="font-medium">Empowering 500+ families since 2009</span>
            </div>
          </div>
          {/* Mission Image */}
          <div className="flex flex-col items-center order-2">
            <div className="relative w-full">
              <img src="/IMG_6267-scaled (1).jpg" alt="Mission - Community children celebrating" className="rounded-lg shadow-md w-full max-w-md mx-auto" />
              <div className="absolute bottom-4 right-4 bg-[#facc15] text-[#111827] px-4 py-2 rounded-md shadow-md font-semibold text-sm">
                15+ Years of Service
              </div>
            </div>
          </div>
          {/* Vision Image */}
          <div className="flex flex-col items-center order-3 md:order-3">
            <div className="relative w-full">
              <img src="/US-College-Life-101-Program-2.jpg" alt="Vision - College Life Program participants" className="rounded-lg shadow-md w-full max-w-md mx-auto" />
              <div className="absolute top-4 left-4 px-4 py-2 rounded-md shadow-md font-semibold text-sm" style={{backgroundColor: '#FACC15', color: '#111827'}}>
                85% Success Rate
              </div>
            </div>
          </div>
          {/* Vision Content */}
          <div className="flex flex-col justify-start order-4 md:order-4">
            <h2 className="font-bold mb-4" style={{color: '#0073E6', fontSize: '36px'}}>What We Do</h2>
            <p className="text-[#374151] mb-4" style={{fontSize: '18px'}}>The Refugee and Immigrant Association exists to provide support and relief to new members of our community.</p>
            <p className="text-[#374151] mb-4" style={{fontSize: '18px'}}>One of our goals is to help with integration into the existing communities by providing connections between long-time residents and new arrivals.</p>
            <div className="inline-flex items-center mt-4 rounded-md" style={{backgroundColor: '#2563eb', color: '#fff', padding: '0.75rem 1.25rem'}}>
              <Globe className="h-5 w-5 mr-2" />
              <span className="font-medium">Building inclusive communities together</span>
            </div>
          </div>
        </div>
      </section>

      {/* Approach & Partnership Section (new design) */}
      <section className="w-full bg-white py-20 px-4 md:px-8" style={{fontFamily: 'Inter, sans-serif'}}>
        {/* Values Intro */}
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="font-bold" style={{fontSize: '2rem', color: '#0f172a'}}>Our Values</h2>
        </div>
        {/* Values Cards: 3 in first row, 2 centered in second row */}
        <div className="max-w-5xl mx-auto flex flex-col gap-8 mb-16">
          {/* First row: 3 cards */}
          <div className="flex flex-col md:flex-row gap-6 justify-center">
            <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center flex-1 min-w-[220px]" style={{boxShadow: '0 4px 12px rgba(0,0,0,0.05)'}}>
              <HandHeart className="h-10 w-10 mb-4" style={{color: '#1e3a8a'}} />
              <h3 className="font-bold text-xl mb-2" style={{color: '#0f172a'}}>Respect</h3>
              <p className="text-base" style={{color: '#0f172a'}}>We treat all people with dignity and respect that they rightfully deserve.</p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center flex-1 min-w-[220px]" style={{boxShadow: '0 4px 12px rgba(0,0,0,0.05)'}}>
              <ShieldCheck className="h-10 w-10 mb-4" style={{color: '#2563eb'}} />
              <h3 className="font-bold text-xl mb-2" style={{color: '#0f172a'}}>Integrity</h3>
              <p className="text-base" style={{color: '#0f172a'}}>We strive to conduct business in a way that is honest, transparent, and ethical.</p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center flex-1 min-w-[220px]" style={{boxShadow: '0 4px 12px rgba(0,0,0,0.05)'}}>
              <Users className="h-10 w-10 mb-4" style={{color: '#facc15'}} />
              <h3 className="font-bold text-xl mb-2" style={{color: '#0f172a'}}>Diversity</h3>
              <p className="text-base" style={{color: '#0f172a'}}>We offer equal opportunities to all customers regardless of race, gender, religion or sexual orientation.</p>
            </div>
          </div>
          {/* Second row: 2 cards centered */}
          <div className="flex flex-col md:flex-row gap-6 justify-center">
            <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center flex-1 min-w-[220px] max-w-[350px]" style={{boxShadow: '0 4px 12px rgba(0,0,0,0.05)'}}>
              <Home className="h-10 w-10 mb-4" style={{color: '#1e3a8a'}} />
              <h3 className="font-bold text-xl mb-2" style={{color: '#0f172a'}}>Community</h3>
              <p className="text-base" style={{color: '#0f172a'}}>We collaborate with other agencies and businesses in the community to provide services more efficiently and effectively.</p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 flex flex-col items-center text-center flex-1 min-w-[220px] max-w-[350px]" style={{boxShadow: '0 4px 12px rgba(0,0,0,0.05)'}}>
              <Sparkles className="h-10 w-10 mb-4" style={{color: '#2563eb'}} />
              <h3 className="font-bold text-xl mb-2" style={{color: '#0f172a'}}>Innovation</h3>
              <p className="text-base" style={{color: '#0f172a'}}>We embrace continuous improvement, bold creativity and change.</p>
            </div>
          </div>
        </div>
        {/* Partnership Model Split Section */}
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center gap-12 md:gap-16">
          {/* Left: Text/Gradient */}
          <div className="w-full md:w-3/5 bg-gradient-to-r from-[#1e3a8a] to-[#2563eb] rounded-xl p-8 md:p-12 flex flex-col justify-center">
            <h3 className="font-bold mb-4" style={{fontSize: '2rem', color: '#fff'}}>Community Partnership Model</h3>
            <p className="text-lg mb-6" style={{color: '#f1f5f9'}}>We work closely with local organizations, schools, employers, and community leaders to create a comprehensive support network that extends far beyond our direct services.</p>
            <ul className="space-y-3">
              <li className="flex items-center text-base" style={{color: '#facc15'}}><ArrowRight className="h-5 w-5 mr-3" />Collaborative program design</li>
              <li className="flex items-center text-base" style={{color: '#facc15'}}><ArrowRight className="h-5 w-5 mr-3" />Shared resource development</li>
              <li className="flex items-center text-base" style={{color: '#facc15'}}><ArrowRight className="h-5 w-5 mr-3" />Community advocacy initiatives</li>
            </ul>
          </div>
          {/* Right: Image */}
          <div className="w-full md:w-2/5 flex justify-center">
            <img src="/IMG_9829 (1).jpg" alt="Women's empowerment program participants" className="rounded-lg shadow-md object-cover w-full max-w-md h-72 md:h-80" />
          </div>
        </div>
      </section>

      {/* Want to Make a Difference & Newsletter */}
      <VolunteerDonateSection />
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
} 