import React from 'react';

const NewsletterSection = () => {
  return (
    <section className="bg-white w-full">
      <div className="py-12 px-4 mx-auto max-w-screen-xl lg:py-20 lg:px-6">
        <div className="mx-auto max-w-4xl grid md:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div>
            <h2 className="mb-4 text-3xl tracking-tight font-extrabold text-gray-900 sm:text-4xl landing-title">Sign up for our newsletter</h2>
            <p className="mb-8 max-w-xl font-light text-gray-500 sm:text-xl">Stay up to date with the latest programs, announcements and events. Feel free to sign up with your email.</p>
          </div>
          {/* Form */}
          <form action="#" className="w-full">
            <div className="flex flex-col sm:flex-row items-stretch mb-3 space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="relative w-full">
                <label htmlFor="email" className="hidden mb-2 text-sm font-medium text-gray-900">Email address</label>
                <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path></svg>
                </div>
                <input className="block p-3 pl-10 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500" placeholder="Enter your email" type="email" id="email" required />
              </div>
              <div className="flex-shrink-0">
                <button type="submit" className="py-3 px-5 w-full text-sm font-medium text-center text-white rounded-lg border cursor-pointer" style={{ backgroundColor: '#00A4F1' }}>
                  Subscribe
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection; 