import React, { createContext, useContext, useState } from 'react';

const VolunteerModalContext = createContext();

export const useVolunteerModal = () => {
  const context = useContext(VolunteerModalContext);
  if (!context) {
    throw new Error('useVolunteerModal must be used within a VolunteerModalProvider');
  }
  return context;
};

export const VolunteerModalProvider = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const value = {
    isModalOpen,
    openModal,
    closeModal
  };

  return (
    <VolunteerModalContext.Provider value={value}>
      {children}
    </VolunteerModalContext.Provider>
  );
};
