import React, { createContext, useContext, useState, useEffect } from 'react';

const HeroImageContext = createContext();

export const useHeroImage = () => {
  const context = useContext(HeroImageContext);
  if (!context) {
    throw new Error('useHeroImage must be used within a HeroImageProvider');
  }
  return context;
};

export const HeroImageProvider = ({ children }) => {
  const heroImages = [
    '/RIA-Recognition-Dinner-Pic-4.jpg',
    '/US-College-Life-101-Program-2.jpg',
    '/IMG_9829 (1).jpg',
    '/IMG_6267-scaled (1).jpg'
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => 
        prevIndex === heroImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, [heroImages.length]);

  const value = {
    heroImages,
    currentImageIndex,
    setCurrentImageIndex,
    getCurrentImage: () => heroImages[currentImageIndex]
  };

  return (
    <HeroImageContext.Provider value={value}>
      {children}
    </HeroImageContext.Provider>
  );
};

export default HeroImageContext;