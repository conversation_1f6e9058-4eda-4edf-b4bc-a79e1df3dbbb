
import React, { createContext, useContext, useState } from 'react';

const DonationModalContext = createContext();

export const useDonationModal = () => {
  const context = useContext(DonationModalContext);
  if (!context) {
    throw new Error('useDonationModal must be used within a DonationModalProvider');
  }
  return context;
};

export const DonationModalProvider = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const value = {
    isModalOpen,
    openModal,
    closeModal
  };

  return (
    <DonationModalContext.Provider value={value}>
      {children}
    </DonationModalContext.Provider>
  );
};
