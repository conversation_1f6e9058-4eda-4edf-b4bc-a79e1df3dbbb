import React from 'react';
import { ExternalLink, Phone, MapPin, Mail, Clock, Users, Heart, BookOpen, Briefcase, Home, Car, GraduationCap, Shield, Building, Facebook, Twitter, Instagram } from 'lucide-react';
import NewsletterSection from './NewsletterSection';
import VolunteerDonateSection from './VolunteerDonateSection';

const linnCountyResources = [
  {
    category: "Linn County Government",
    icon: Building,
    color: "bg-blue-600",
    items: [
      { name: "Linn County Administration", phone: "(*************", address: "935 2nd St SW, Cedar Rapids, IA 52404", description: "Main county government office" },
      { name: "Linn County Public Health", phone: "(*************", address: "501 13th St NW, Cedar Rapids, IA 52405", description: "Public health services and programs" },
      { name: "Linn County Human Services", phone: "(*************", address: "1240 26th Ave Ct SW, Cedar Rapids, IA 52404", description: "Social services and assistance programs" }
    ]
  },
  {
    category: "Healthcare Services",
    icon: Heart,
    color: "bg-red-500",
    items: [
      { name: "UnityPoint Health - St. Luke's", phone: "(*************", address: "1026 A Ave NE, Cedar Rapids, IA 52402", description: "Major hospital and emergency services" },
      { name: "Mercy Medical Center", phone: "(*************", address: "701 10th St SE, Cedar Rapids, IA 52403", description: "Full-service medical center" },
      { name: "Community Health Free Clinic", phone: "(*************", address: "1030 5th Ave SE, Cedar Rapids, IA 52403", description: "Free healthcare for uninsured patients" }
    ]
  },
  {
    category: "Housing & Shelter",
    icon: Home,
    color: "bg-green-600",
    items: [
      { name: "Linn County Housing Agency", phone: "(*************", address: "935 2nd St SW, Cedar Rapids, IA 52404", description: "Housing assistance and voucher programs" },
      { name: "Willis Dady Homeless Services", phone: "(*************", address: "1251 3rd St SE, Cedar Rapids, IA 52401", description: "Emergency shelter and housing services" },
      { name: "Habitat for Humanity of Linn County", phone: "(*************", address: "2095 Wiley Blvd SW, Cedar Rapids, IA 52404", description: "Affordable homeownership opportunities" }
    ]
  },
  {
    category: "Employment & Education",
    icon: Briefcase,
    color: "bg-purple-600",
    items: [
      { name: "Iowa Workforce Development - Cedar Rapids", phone: "(*************", address: "1540 2nd Ave SE, Cedar Rapids, IA 52403", description: "Job placement and career services" },
      { name: "Kirkwood Community College - Cedar Rapids", phone: "(*************", address: "6301 Kirkwood Blvd SW, Cedar Rapids, IA 52406", description: "Career training and ESL classes" },
      { name: "Cedar Rapids Public Library", phone: "(*************", address: "450 5th Ave SE, Cedar Rapids, IA 52401", description: "Free computer access and literacy programs" }
    ]
  },
  {
    category: "Legal & Immigration Services",
    icon: BookOpen,
    color: "bg-indigo-600",
    items: [
      { name: "Iowa Legal Aid - Linn County", phone: "(*************", address: "1540 2nd Ave SE #100, Cedar Rapids, IA 52403", description: "Free legal services for low-income residents" },
      { name: "Catherine McAuley Center", phone: "(*************", address: "1606 1st Ave SE, Cedar Rapids, IA 52402", description: "Immigration legal assistance and advocacy" },
      { name: "Legal Clinic of Cedar Rapids", phone: "(*************", address: "1540 2nd Ave SE, Cedar Rapids, IA 52403", description: "Low-cost legal services" }
    ]
  },
  {
    category: "Transportation",
    icon: Car,
    color: "bg-teal-600",
    items: [
      { name: "Cedar Rapids Transit", phone: "(*************", address: "1045 7th St SE, Cedar Rapids, IA 52401", description: "Public bus transportation" },
      { name: "CRANDIC Railway", phone: "(*************", description: "Regional train service between Cedar Rapids and Iowa City" },
      { name: "Medical Transport (Linn County)", phone: "(*************", description: "Transportation for medical appointments" }
    ]
  },
  {
    category: "Community Organizations",
    icon: Users,
    color: "bg-orange-600",
    items: [
      { name: "United Way of East Central Iowa", phone: "(*************", address: "425 2nd St SE #710, Cedar Rapids, IA 52401", description: "Community support and resources coordination" },
      { name: "Foundation 2 Crisis Services", phone: "(*************", address: "1540 2nd Ave SE, Cedar Rapids, IA 52403", description: "Crisis intervention and mental health support" },
      { name: "Cedar Valley Coalition", phone: "(*************", address: "1251 3rd St SE, Cedar Rapids, IA 52401", description: "Community advocacy and support services" }
    ]
  },
  {
    category: "Food Assistance",
    icon: Heart,
    color: "bg-pink-600",
    items: [
      { name: "HACAP Food Reservoir", phone: "(*************", address: "1515 Hawkeye Dr, Hiawatha, IA 52233", description: "Food bank and assistance programs" },
      { name: "Salvation Army - Cedar Rapids", phone: "(*************", address: "1116 7th St SE, Cedar Rapids, IA 52401", description: "Food pantry and emergency assistance" },
      { name: "Area Substance Abuse Council Food Pantry", phone: "(*************", address: "3601 C St SW, Cedar Rapids, IA 52404", description: "Food assistance and community support" }
    ]
  },
  {
    category: "Cultural & Integration Services",
    icon: Users,
    color: "bg-cyan-600",
    items: [
      { name: "Catherine McAuley Center", phone: "(*************", address: "1606 1st Ave SE, Cedar Rapids, IA 52402", description: "Immigrant integration and advocacy services" },
      { name: "Cedar Rapids Multicultural Center", phone: "(*************", address: "1540 2nd Ave SE, Cedar Rapids, IA 52403", description: "Cultural programs and community events" },
      { name: "International Services at Kirkwood", phone: "(*************", description: "Support services for international students and families" }
    ]
  }
];

const LinnCountyResources = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative text-white overflow-hidden py-20">
        <div className="absolute inset-0">
          <img
            src="/IMG_9829 (1).jpg"
            alt="Linn County resources"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Linn County <span className="text-yellow-400">Resources</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
            Comprehensive services and support available throughout Linn County, Iowa
          </p>
        </div>
      </section>

      {/* County Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">About Linn County</h2>
          <p className="text-lg text-gray-600 leading-relaxed mb-8">
            Linn County, with Cedar Rapids as its largest city, is a major economic and cultural center in Eastern Iowa. 
            The county offers extensive resources for refugees and immigrants, including comprehensive healthcare systems, 
            educational opportunities, employment services, and strong community support networks.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Population</h3>
              <p className="text-gray-600">~230,000 residents</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Briefcase className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Economic Hub</h3>
              <p className="text-gray-600">Major employers</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Heart className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Healthcare</h3>
              <p className="text-gray-600">Multiple hospitals</p>
            </div>
            <div className="text-center">
              <div className="bg-orange-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <GraduationCap className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Education</h3>
              <p className="text-gray-600">Kirkwood College</p>
            </div>
          </div>
        </div>
      </section>

      {/* Resources Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Linn County Services
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Essential resources and support services throughout Linn County
            </p>
          </div>

          <div className="space-y-12">
            {linnCountyResources.map((category, idx) => {
              const IconComponent = category.icon;
              return (
                <div key={idx} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="p-8">
                    <div className="flex items-center mb-8">
                      <div className={`${category.color} p-4 rounded-lg mr-6`}>
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-3xl font-bold text-gray-900">{category.category}</h3>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                      {category.items.map((item, itemIdx) => (
                        <div key={itemIdx} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                          <h4 className="text-xl font-semibold text-gray-900 mb-3">{item.name}</h4>
                          <div className="space-y-2 mb-4">
                            <div className="flex items-start">
                              <Phone className="h-4 w-4 text-blue-600 mr-2 mt-1 flex-shrink-0" />
                              <a href={`tel:${item.phone}`} className="text-blue-600 hover:text-blue-800 font-medium">
                                {item.phone}
                              </a>
                            </div>
                            {item.address && (
                              <div className="flex items-start">
                                <MapPin className="h-4 w-4 text-green-600 mr-2 mt-1 flex-shrink-0" />
                                <span className="text-gray-600 text-sm">{item.address}</span>
                              </div>
                            )}
                          </div>
                          <p className="text-gray-600">{item.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Special Programs Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Special Programs in Linn County
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-xl">
              <div className="flex items-center mb-4">
                <GraduationCap className="h-8 w-8 text-blue-600 mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">New American Centers</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Specialized programs at Kirkwood Community College designed specifically for refugees and immigrants, 
                including English language learning, job training, and cultural orientation programs.
              </p>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-xl">
              <div className="flex items-center mb-4">
                <Users className="h-8 w-8 text-green-600 mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Cultural Integration</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Cedar Rapids has a strong network of community organizations that provide cultural integration support, 
                helping new residents connect with their communities while maintaining their cultural heritage.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="relative w-full py-20 bg-gray-900 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 w-full h-full">
          <img
            src="/IMG_6267-scaled (1).jpg"
            alt="Group of volunteers holding food donation boxes"
            className="w-full h-full object-cover"
            style={{ filter: 'brightness(0.7)' }}
          />
          <div className="absolute inset-0 bg-black opacity-40"></div>
        </div>
        {/* Overlay Content */}
        <div className="relative z-10 flex flex-col items-center justify-center min-h-[420px] text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-white drop-shadow-lg landing-title">Looking for Assistance?</h2>
          <p className="text-lg md:text-2xl text-white mb-10 max-w-2xl">
            If you need immediate assistance or have questions about any of these resources, please don't hesitate to contact the <span className="font-bold bg-yellow-400 text-deep-blue px-2 rounded">Refugee and Immigrant Association</span>. We're here to help you navigate these services and connect you with the right support.
          </p>
          <div className="flex justify-center">
            <button className="text-white px-6 py-3 rounded-md text-base font-semibold shadow-md transition" style={{ backgroundColor: '#FACC15' }}>
              Contact Us
            </button>
          </div>
        </div>
      </section>
      <NewsletterSection />
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LinnCountyResources;
