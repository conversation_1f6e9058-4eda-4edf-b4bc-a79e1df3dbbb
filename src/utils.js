// Shared utility functions

export const getCategoryColor = (category) => {
  const colors = {
    'Education': 'bg-blue-500',
    'Empowerment': 'bg-orange-500',
    'Community': 'bg-green-500',
    'Integration': 'bg-purple-500',
    'Skills': 'bg-yellow-500',
    'Support': 'bg-teal-500',
    'Media': 'bg-red-500' // Adding Media category for the newspaper program
  };
  return colors[category] || 'bg-gray-500';
};

// Function to get the hex color value for inline styles
export const getCategoryColorHex = (category) => {
  const colors = {
    'Education': '#3B82F6',      // blue-500
    'Empowerment': '#F97316',    // orange-500
    'Community': '#10B981',      // green-500
    'Integration': '#8B5CF6',    // purple-500
    'Skills': '#EAB308',         // yellow-500
    'Support': '#14B8A6',        // teal-500
    'Media': '#EF4444'           // red-500
  };
  return colors[category] || '#6B7280'; // gray-500
};