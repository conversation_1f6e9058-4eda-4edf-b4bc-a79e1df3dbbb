
import React, { useState, useEffect } from 'react';
import { Facebook, Twitter, Instagram } from 'lucide-react';
import WhoWeAre from './WhoWeAre';
import Board from './Board';
import Programs from './Programs';
import Events from './Events';
import GeneralResources from './GeneralResources';
import JohnsonCountyResources from './JohnsonCountyResources';
import LinnCountyResources from './LinnCountyResources';
import { BrowserRouter, Routes, Route, useLocation } from 'react-router-dom';
import Navbar from './Navbar';
import Blogs from './Blogs';
import Gallery from './Gallery';
import BlogPost from './BlogPost';
import Home from './Home';
import Program from './Program';
import Event from './Event';
import About from './About';
import Contact from './Contact';
import Donate from './Donate';
import { HeroImageProvider } from './contexts/HeroImageContext';
import { VolunteerModalProvider } from './contexts/VolunteerModalContext';
import { DonationModalProvider } from './contexts/DonationModalContext';
import VolunteerFormModal from './components/VolunteerFormModal';
import DonationModal from './components/DonationModal';

function AppContent() {
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <VolunteerFormModal />
      <DonationModal />
      <div className={isHomePage ? '' : 'pt-20'}>
        <Routes>
          <Route path="/about" element={<About />} />
          <Route path="/about/who-we-are" element={<WhoWeAre />} />
          <Route path="/about/board" element={<Board />} />
          <Route path="/programs" element={<Programs />} />
          <Route path="/program/:slug" element={<Program />} />
          <Route path="/events" element={<Events />} />
          <Route path="/event/:slug" element={<Event />} />
          <Route path="/resources/general" element={<GeneralResources />} />
          <Route path="/resources/johnson-county" element={<JohnsonCountyResources />} />
          <Route path="/resources/linn-county" element={<LinnCountyResources />} />
          <Route path="/blogs" element={<Blogs />} />
          <Route path="/blog/:slug" element={<BlogPost />} />
          <Route path="/gallery" element={<Gallery />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/donate" element={<Donate />} />
          <Route path="/" element={<Home />} />
        </Routes>
      </div>
    </div>
  );
}

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleAboutDropdown = () => setIsAboutDropdownOpen((v) => !v);
  const closeAboutDropdown = () => setIsAboutDropdownOpen(false);

  // Close dropdown on outside click (desktop)
  useEffect(() => {
    if (!isAboutDropdownOpen) return;
    function handleClick(e) {
      if (!e.target.closest('.about-dropdown-parent')) {
        setIsAboutDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [isAboutDropdownOpen]);

  return (
    <BrowserRouter>
      <DonationModalProvider>
        <VolunteerModalProvider>
          <HeroImageProvider>
            <AppContent />
          </HeroImageProvider>
        </VolunteerModalProvider>
      </DonationModalProvider>
    </BrowserRouter>
  );
}

export default App;
