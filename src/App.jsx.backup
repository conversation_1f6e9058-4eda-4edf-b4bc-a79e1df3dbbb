import React, { useState, useEffect, useRef } from 'react';
import { Heart, Users, HandHeart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Menu, X, GraduationCap, Briefcase } from 'lucide-react';
import { getCategoryColor, getCategoryColorHex } from './utils';
import VolunteerDonateSection from './VolunteerDonateSection';
import NewsletterSection from './NewsletterSection';
import WhoWeAre from './WhoWeAre';
import Board from './Board';
import Programs from './Programs';
import Events from './Events';
import GeneralResources from './GeneralResources';
import JohnsonCountyResources from './JohnsonCountyResources';
import LinnCountyResources from './LinnCountyResources';
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import Navbar from './Navbar';
import Blogs from './Blogs';
import Gallery from './Gallery';
import BlogPost from './BlogPost';

const heroImages = [
  "/IMG_6267-scaled (1).jpg",
  "/US-College-Life-101-Program-2.jpg",
  "/RIA-Recognition-Dinner-Pic-4.jpg",
  "/IMG_9829 (1).jpg"
];

const programs = [
  {
    title: 'DRIVING LESSON',
    category: 'Education',
    image: '/US-College-Life-101-Program-2.jpg',
    description: 'Helping refugees and immigrants gain independence and mobility through professional driving lessons.'
  },
  {
    title: 'Refugee Women Empowerment Program',
    category: 'Empowerment',
    image: '/IMG_9829 (1).jpg',
    description: 'Supporting refugee women with leadership, skills training, and community-building opportunities.'
  },
  {
    title: 'Sewing Machine Program',
    category: 'Skills',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    description: 'Providing sewing machines and training to foster self-reliance and entrepreneurship.'
  },
  {
    title: 'Academic Improvement Program',
    category: 'Education',
    image: '/IMG_6267-scaled (1).jpg',
    description: 'Tutoring and academic support for children and adults to help them succeed in school.'
  },
  {
    title: 'School Transportation Program',
    category: 'Support',
    image: '/US-College-Life-101-Program-2.jpg',
    description: 'Ensuring safe and reliable transportation for students to and from school.'
  },
  {
    title: 'Refugee and Immigrant Newspaper',
    category: 'Media',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    description: 'A community newspaper sharing stories, news, and resources for refugees and immigrants.'
  },
  {
    title: 'Refugee and Immigrant Voice Group',
    category: 'Community',
    image: '/IMG_9829 (1).jpg',
    description: 'A platform for refugees and immigrants to share their voices and advocate for their needs.'
  },
];

const events = [
  {
    title: 'Children are Precious They are the Future of the World',
    date: { day: '16', month: 'March' },
    meta: 'November 5, 2021',
    image: '/US-College-Life-101-Program-2.jpg',
    link: '#'
  },
  {
    title: 'School For African Poor Children & People',
    date: { day: '22', month: 'April' },
    meta: 'November 5, 2021',
    image: '/IMG_9829 (1).jpg',
    link: '#'
  },
  {
    title: 'Children are Precious They are the Future of the World',
    date: { day: '16', month: 'May' },
    meta: 'November 5, 2021',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    link: '#'
  }
];

const blogPosts = [
  {
    id: 1,
    slug: 'empowering-refugee-students-college-preparation',
    image: '/US-College-Life-101-Program-2.jpg',
    date: 'March 15, 2024',
    title: 'Empowering Refugee Students Through College Preparation',
    category: 'Education',
    description: 'Our US College Life 101 program continues to help refugee students navigate the complexities of higher education, providing them with essential skills and knowledge.',
    author: 'Sarah Johnson'
  },
  {
    id: 2,
    slug: 'women-leadership-summit-breaking-barriers',
    image: '/IMG_9829 (1).jpg',
    date: 'March 10, 2024',
    title: 'Women Leadership Summit: Breaking Barriers Together',
    category: 'Empowerment',
    description: 'Celebrating the achievements of refugee women leaders in our community and discussing strategies for continued growth and empowerment.',
    author: 'Maria Rodriguez'
  },
  {
    id: 3,
    slug: 'community-recognition-dinner-success-stories',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    date: 'March 5, 2024',
    title: 'Community Recognition Dinner: Celebrating Success Stories',
    category: 'Community',
    description: 'An evening dedicated to recognizing the outstanding achievements of refugees and immigrants who have made significant contributions to our community.',
    author: 'Ahmed Hassan'
  }
];

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const sliderRef = useRef(null);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleAboutDropdown = () => setIsAboutDropdownOpen((v) => !v);
  const closeAboutDropdown = () => setIsAboutDropdownOpen(false);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentImageIndex(prevIndex => (prevIndex + 1) % heroImages.length);
    }, 5000);
    return () => clearInterval(intervalId);
  }, []);

  // Close dropdown on outside click (desktop)
  useEffect(() => {
    if (!isAboutDropdownOpen) return;
    function handleClick(e) {
      if (!e.target.closest('.about-dropdown-parent')) {
        setIsAboutDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [isAboutDropdownOpen]);



  return (
    <BrowserRouter>
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="pt-20">
          <Routes>
            <Route path="/about/who-we-are" element={<WhoWeAre />} />
            <Route path="/about/board" element={<Board />} />
            <Route path="/programs" element={<Programs />} />
            <Route path="/events" element={<Events />} />
            <Route path="/resources/general" element={<GeneralResources />} />
            <Route path="/resources/johnson-county" element={<JohnsonCountyResources />} />
            <Route path="/resources/linn-county" element={<LinnCountyResources />} />
            <Route path="/blogs" element={<Blogs />} />
            <Route path="/blog/:slug" element={<BlogPost />} />
            <Route path="/gallery" element={<Gallery />} />
            <Route path="/" element={
              <>
                {/* Hero Section */}
                <section className="relative text-white overflow-hidden h-screen">
                  <div className="absolute inset-0">
                    <img
                      key={currentImageIndex}
                      src={heroImages[currentImageIndex]}
                      alt="Community event"
                      className="w-full h-full object-cover animate-zoom"
                    />
                    <div className="absolute inset-0 bg-black opacity-50"></div>
                  </div>
                  <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center justify-center">
                    <div className="text-center">
                      <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                        Welcome to the <span className="text-yellow-400">Refugee and Immigrant Association.</span>
                      </h1>
                      <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
                        We are a group led in large part by former refugees helping new refugees in both <span className="text-yellow-400">Johnson</span> and <span className="text-yellow-400">Linn</span> Counties in Iowa.
                      </p>
                    </div>
                  </div>
                </section>
                {/* Who We Are Section */}
                <section id="about" className="py-24 bg-white">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid lg:grid-cols-5 gap-16 items-center">
                      {/* Image Collage */}
                      <div className="lg:col-span-3 grid grid-cols-2 gap-6">
                        <div className="space-y-6">
                          <img 
                            src="/IMG_9829 (1).jpg" 
                            alt="Community members" 
                            className="rounded-lg shadow-xl w-full hover:scale-105 transition-transform duration-300"
                          />
                          <img 
                            src="/US-College-Life-101-Program-2.jpg" 
                            alt="Educational program" 
                            className="rounded-lg shadow-xl w-full hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div>
                          <img 
                            src="/RIA-Recognition-Dinner-Pic-4.jpg" 
                            alt="Community dinner" 
                            className="rounded-lg shadow-xl w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      </div>
                      {/* Text Content */}
                      <div className="lg:col-span-2">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                          Who <span className="text-deep-blue">We Are</span>
                        </h2>
                        <div className="border-l-4 border-yellow-500 pl-6 mt-6">
                          <p className="text-lg text-gray-700 leading-relaxed">
                            Our team is made up of people from all walks of life including those whom have never lived outside of Iowa to those who have been born in far-reaching places of the world. We come together to provide a welcoming atmosphere to our community.
                          </p>
                        </div>
                        <div className="mt-12 space-y-12">
                          {/* Mission */}
                          <div className="flex items-start">
                            <div className="flex-shrink-0">
                              <img src="/missionicon.svg" alt="Mission Icon" className="h-12 w-12" />
                            </div>
                            <div className="ml-6">
                              <h4 className="text-xl font-bold text-gray-900">Mission</h4>
                              <p className="mt-1 text-base text-gray-600">
                                We aim to provide community civic education, issue awareness, and financial support for refugees and immigrants from around the world.
                              </p>
                            </div>
                          </div>
                          {/* Vision */}
                          <div className="flex items-start">
                            <div className="flex-shrink-0">
                              <img src="/achievement.svg" alt="Achievement Icon" className="h-12 w-12" />
                            </div>
                            <div className="ml-6">
                              <h4 className="text-xl font-bold text-gray-900">Vision</h4>
                              <p className="mt-1 text-base text-gray-600">
                                Our vision is of a community that participates and contributes in helping refugees and immigrants become self-reliant, independent, and integrated.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                {/* Programs Section */}
                <section id="programs" className="bg-[#F9F9F9] py-20">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                      <span className="inline-block bg-yellow-400 text-[#1A1A1A] text-lg font-extrabold px-8 py-3 rounded-full uppercase tracking-wider mb-6 shadow-md" style={{fontSize: '1.35rem', letterSpacing: '0.05em'}}>Our Programs</span>
                      <h2 className="text-4xl md:text-5xl font-bold text-[#1A1A1A] mb-4" style={{letterSpacing: 'normal'}}>It's About Impact, Good History</h2>
                    </div>
                    <div className="relative flex items-center">
                      <button
                        onClick={() => setCarouselIndex(i => Math.max(i - 1, 0))}
                        className={`absolute left-0 z-10 bg-white shadow-lg rounded-full p-2 hover:bg-yellow-400 transition ${carouselIndex === 0 ? 'opacity-30 cursor-not-allowed' : ''}`}
                        disabled={carouselIndex === 0}
                        style={{top: '50%', transform: 'translateY(-50%)'}}
                      >
                        <svg className="h-6 w-6 text-gray-700" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" /></svg>
                      </button>
                      <div className="w-full">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-[40px] justify-items-center">
                          {programs.slice(carouselIndex, carouselIndex + 3).map((program, idx) => (
                            <div key={idx} className="relative bg-[#FFFFFF] rounded-2xl shadow-[0px_2px_10px_rgba(0,0,0,0.05)] hover:scale-[1.03] transition-transform duration-300 w-full max-w-md" style={{fontFamily: 'Sans-serif', minHeight: '480px'}}>
                              <div className="relative">
                                <img src={program.image} alt={program.title} className="w-full aspect-[4/3] object-cover rounded-t-2xl" />
                                <span
                                  className="absolute left-6 bottom-6 text-white text-[0.9rem] font-semibold px-3 py-2 rounded"
                                  style={{ backgroundColor: getCategoryColorHex(program.category) }}
                                >
                                  {program.category}
                                </span>
                              </div>
                              <div className="p-8">
                                <h3 className="text-2xl font-bold text-[#1A1A1A] mt-4" style={{fontWeight: 700}}>{program.title}</h3>
                                <p className="text-lg text-[#666666] mt-4" style={{lineHeight: 1.6}}>{program.description}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <button
                        onClick={() => setCarouselIndex(i => Math.min(i + 1, programs.length - 3))}
                        className={`absolute right-0 z-10 bg-white shadow-lg rounded-full p-2 hover:bg-yellow-400 transition ${carouselIndex >= programs.length - 3 ? 'opacity-30 cursor-not-allowed' : ''}`}
                        disabled={carouselIndex >= programs.length - 3}
                        style={{top: '50%', transform: 'translateY(-50%)'}}
                      >
                        <svg className="h-6 w-6 text-gray-700" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
                      </button>
                    </div>
                  </div>
                </section>

                {/* Events Section */}
                <section id="events" className="py-20 bg-[#F9F9F9]">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid lg:grid-cols-5 gap-[60px] items-start">
                      {/* Left column: CTA */}
                      <div className="lg:col-span-2">
                        <div className="text-[#2563eb] font-bold mb-6 text-4xl md:text-5xl leading-tight" style={{fontWeight: 700}}>
                          Join Our Upcoming Events
                        </div>
                        <div className="text-[#1A1A1A] text-2xl font-bold mb-4" style={{fontSize: '1.9rem', fontWeight: 700}}>
                          Check out our <a href="https://www.facebook.com/RIAssociation/" target="_blank" rel="noopener noreferrer" style={{color: '#01A0FF', textDecoration: 'underline'}}>Facebook page</a> for the latest updates on our upcoming events!
                        </div>
                      </div>
                      {/* Right column: Events list */}
                      <div className="lg:col-span-3 flex flex-col gap-6">
                        {events.map((event, idx) => (
                          <div key={idx} className="relative flex bg-white rounded-xl shadow-lg overflow-hidden min-h-[170px]">
                            <div className="relative w-48 flex-shrink-0">
                              <img src={event.image} alt={event.title} className="w-full h-full aspect-[4/3] object-cover rounded-xl" style={{borderRadius: '8px'}} />
                              <div className="absolute left-3 bottom-3 bg-yellow-400 text-deep-blue font-bold px-3 py-1 rounded text-xs shadow" style={{fontSize: '0.85rem'}}>
                                <span className="block text-base font-bold" style={{fontSize: '1.1rem'}}>{event.date.day}</span>
                                <span className="uppercase" style={{fontSize: '0.75rem'}}>{event.date.month}</span>
                              </div>
                            </div>
                            <div className="flex-1 flex flex-col justify-center pl-6 pr-4 py-4">
                              <div className="text-[#2563eb] text-sm mb-1">{event.meta}</div>
                              <div className="text-[#1A1A1A] font-bold mb-2" style={{fontSize: '1.2rem'}}>{event.title}</div>
                              <a href={event.link} className="text-[#2563eb] text-sm font-semibold hover:underline flex items-center mt-2 group">
                                Read More <span className="ml-1 group-hover:translate-x-1 transition-transform">→</span>
                              </a>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </section>

                {/* Impact Section */}
                <section id="impact" className="py-20 bg-deep-blue text-white">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                      <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Our <span className="text-yellow-400">Impact</span>
                      </h2>
                      <p className="text-xl text-blue-100 max-w-3xl mx-auto">
                        Creating lasting change and building brighter futures for refugee families in our community.
                      </p>
                    </div>
                    <div className="grid md:grid-cols-3 gap-8 text-center">
                      <div className="bg-white text-gray-900 p-8 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="/icons8-family-100.png" alt="Families Supported Icon" className="w-16 h-16 mb-4 object-contain" />
                        <div className="text-5xl font-bold text-deep-blue mb-4">500+</div>
                        <div className="text-lg font-medium">Families Supported</div>
                      </div>
                      <div className="bg-white text-gray-900 p-8 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="/icons8-graduation-100.png" alt="HS Graduation Icon" className="w-16 h-16 mb-4 object-contain" />
                        <div className="text-5xl font-bold text-deep-blue mb-4">95%</div>
                        <div className="text-lg font-medium">HS Graduation Rate</div>
                      </div>
                      <div className="bg-white text-gray-900 p-8 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="/job.png" alt="Job Placements Icon" className="w-16 h-16 mb-4 object-contain" />
                        <div className="text-5xl font-bold text-deep-blue mb-4">300+</div>
                        <div className="text-lg font-medium">Job Placements</div>
                      </div>
                    </div>
                  </div>
                </section>

                {/* Blog Section */}
                <section id="blog" className="py-20 bg-white">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-4xl md:text-5xl font-bold text-center text-[#1A1A1A] mb-4">News & Articles</h2>
                    <p className="text-xl text-center text-[#666] mb-12">Explore impactful stories and real-world updates that show how your support is making a difference.</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {blogPosts.map((post, idx) => (
                        <div
                          key={idx}
                          className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 flex flex-col"
                        >
                          {/* Image */}
                          <div className="relative h-48 overflow-hidden">
                            <img
                              src={post.image}
                              alt={post.title}
                              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                            />
                          </div>
                          {/* Content */}
                          <div className="p-6 flex flex-col flex-1">
                            {/* Category and Date */}
                            <div className="flex items-center justify-between mb-3">
                              <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold text-white ${getCategoryColor(post.category)}`}>
                                {post.category}
                              </span>
                              <span className="text-xs text-gray-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                {post.date}
                              </span>
                            </div>
                            {/* Title */}
                            <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2 leading-tight">
                              {post.title}
                            </h3>
                            {/* Description */}
                            <p className="text-gray-600 text-sm mb-4 flex-1 line-clamp-3">
                              {post.description}
                            </p>
                            {/* Author and Read More */}
                            <div className="flex items-center justify-between mt-auto pt-4 border-t border-gray-100">
                              <span className="text-xs text-gray-500">
                                by <span className="font-medium text-gray-700">{post.author}</span>
                              </span>
                              <Link
                                to={`/blog/${post.slug}`}
                                className="text-blue-600 hover:text-blue-800 text-sm font-semibold flex items-center group"
                              >
                                Read More
                                <span className="ml-1 group-hover:translate-x-1 transition-transform">→</span>
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </section>

                {/* Ways to Help */}
                <VolunteerDonateSection />
                <NewsletterSection />

                {/* Footer */}
                <footer className="bg-gray-800 text-white">
                  <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                      <div className="space-y-4">
                        <p className="text-gray-400">
                          Empowering refugee families to build brighter futures.
                        </p>
                        <div className="flex space-x-4">
                          <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                          <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                          <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold mb-4">Programs</h4>
                        <ul className="space-y-2 text-gray-400">
                          <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
                        <ul className="space-y-2 text-gray-400">
                          <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                          <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold mb-4">Contact</h4>
                        <div className="space-y-3 text-gray-400">
                          <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                          <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                          <div className="mb-2">(319) 491-3486</div>
                          <div className="font-semibold text-white">Jean-Paul:</div>
                          <div>(319) 383-9819</div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
                      &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
                    </div>
                  </div>
                </footer>
              </>
            } />
          </Routes>
        </div>
      </div>
    </BrowserRouter>
  );
}

export default App;
