
import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Calendar, MapPin, Users, Target, ArrowRight } from 'lucide-react';
import NewsletterSection from './NewsletterSection';

const Program = () => {
  const { slug } = useParams();
  
  // Sample programs data with comprehensive content
  const programs = [
    {
      id: 1,
      slug: 'driving-lesson',
      image: '/US-College-Life-101-Program-2.jpg',
      title: 'DRIVING LESSON',
      category: 'Education',
      description: 'Helping refugees and immigrants gain independence and mobility through professional driving lessons.',
      duration: '8 weeks',
      schedule: 'Saturdays & Sundays, 9:00 AM - 12:00 PM',
      location: 'RIA Driving Training Center',
      participants: '10-12 students per session',
      date: 'December 15, 2024',
      content: `
        <p>The Driving Lesson program is designed to help refugees and immigrants gain the essential skill of driving, providing them with independence and improved access to employment opportunities, healthcare, education, and community services.</p>

        <p>Many refugees and immigrants come from countries where driving is not as common or where traffic rules differ significantly from those in the United States. This program addresses those challenges through comprehensive instruction.</p>

        <h3>Program Components</h3>
        <p>Our driving program includes several key elements:</p>
        <ul>
          <li>Traffic rules and road signs education</li>
          <li>Hands-on driving practice with certified instructors</li>
          <li>Preparation for written and practical driving tests</li>
          <li>Understanding of car insurance and registration</li>
          <li>Safe driving practices and defensive driving techniques</li>
          <li>Navigation and GPS usage training</li>
        </ul>

        <h3>Impact on Independence</h3>
        <p>Having a driver's license dramatically improves the quality of life for refugee families. It enables parents to drive their children to school, attend medical appointments, shop for groceries, and seek employment opportunities that were previously inaccessible.</p>

        <p>The program has helped over 200 individuals obtain their driver's licenses, with a 90% success rate on the first attempt at the DMV test.</p>
      `,
      outcomes: [
        'Driver\'s license test preparation completion',
        '90% success rate on DMV tests',
        'Improved access to employment opportunities',
        'Enhanced family independence and mobility',
        'Better access to essential services'
      ]
    },
    {
      id: 2,
      slug: 'academic-improvement-program',
      image: '/IMG_6267-scaled (1).jpg',
      title: 'Academic Improvement Program',
      category: 'Education',
      description: 'Tutoring and academic support for children and adults to help them succeed in school.',
      duration: '12 weeks',
      schedule: 'Monday to Friday, 4:00 PM - 6:00 PM',
      location: 'Community Learning Centers',
      participants: '30-40 students per session',
      date: 'January 8, 2025',
      content: `
        <p>The Academic Improvement Program provides comprehensive educational support to refugee and immigrant students of all ages, helping them overcome language barriers and academic challenges to succeed in American schools.</p>

        <p>This program recognizes that educational success is crucial for long-term integration and prosperity in the United States, and addresses the unique challenges faced by students from diverse educational backgrounds.</p>

        <h3>Educational Support Services</h3>
        <p>Our program offers comprehensive academic assistance:</p>
        <ul>
          <li>English as a Second Language (ESL) instruction</li>
          <li>Mathematics tutoring and support</li>
          <li>Science and social studies assistance</li>
          <li>Homework help and study skills development</li>
          <li>Test preparation and exam strategies</li>
          <li>College and career counseling</li>
        </ul>

        <h3>Student Success Stories</h3>
        <p>Many students who have participated in our program have gone on to excel in their studies, with 85% showing significant improvement in their grades within one semester.</p>

        <p>The program has helped students gain confidence in their academic abilities and has prepared many for higher education opportunities.</p>
      `,
      outcomes: [
        '85% improvement in student grades',
        'Enhanced English language proficiency',
        'Improved study skills and habits',
        'Increased college enrollment rates',
        'Better integration into school communities'
      ]
    },
    {
      id: 3,
      slug: 'refugee-women-empowerment-program',
      image: '/IMG_9829 (1).jpg',
      title: 'Refugee Women Empowerment Program',
      category: 'Empowerment',
      description: 'Supporting refugee women with leadership, skills training, and community-building opportunities.',
      duration: '16 weeks',
      schedule: 'Wednesdays, 6:00 PM - 8:00 PM',
      location: 'Women\'s Empowerment Center',
      participants: '15-20 women per cohort',
      date: 'February 1, 2025',
      content: `
        <p>The Refugee Women Empowerment Program is designed to support refugee and immigrant women in developing leadership skills, building confidence, and creating pathways to economic independence and community leadership.</p>

        <p>Recognizing the unique challenges faced by refugee women, this program provides a supportive environment where women can develop skills, share experiences, and build networks that will serve them throughout their lives in America.</p>

        <h3>Empowerment Focus Areas</h3>
        <p>The program addresses multiple aspects of women's empowerment:</p>
        <ul>
          <li>Leadership development and public speaking</li>
          <li>Financial literacy and business skills</li>
          <li>Digital literacy and technology training</li>
          <li>Health and wellness education</li>
          <li>Legal rights and advocacy training</li>
          <li>Networking and mentorship opportunities</li>
        </ul>

        <h3>Community Leadership</h3>
        <p>Many program graduates have become leaders in their communities, starting their own businesses, joining school boards, and advocating for refugee rights.</p>

        <p>The program has created a strong network of empowered women who continue to support each other and mentor new participants.</p>
      `,
      outcomes: [
        'Leadership skills development',
        'Economic empowerment and independence',
        'Community leadership positions',
        'Business development and entrepreneurship',
        'Strong peer support networks'
      ]
    }
  ];

  const program = programs.find(p => p.slug === slug);
  
  if (!program) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Program Not Found</h1>
          <p className="text-gray-600 mb-8">The program you're looking for doesn't exist.</p>
          <Link to="/programs" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Back to Programs
          </Link>
        </div>
      </div>
    );
  }

  const relatedPrograms = programs.filter(p => p.slug !== slug).slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section - White background like blog posts */}
      <div className="bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Breadcrumbs */}
          <nav className="flex items-center space-x-2 text-sm mb-8">
            <Link to="/" className="text-gray-400 hover:text-gray-600 transition-colors">
              Home
            </Link>
            <span className="text-gray-400">›</span>
            <Link to="/programs" className="text-gray-400 hover:text-gray-600 transition-colors">
              Programs
            </Link>
            <span className="text-gray-400">›</span>
            <span className="text-gray-600">{program.title}</span>
          </nav>

          {/* Title and Meta */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-4">
              {program.category}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {program.title}
            </h1>
            <div className="flex items-center justify-center space-x-6 text-gray-600 mb-8">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>{program.date}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>{program.participants}</span>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="mb-12">
            <img
              src={program.image}
              alt={program.title}
              className="w-full h-[400px] object-cover rounded-2xl shadow-lg"
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Program Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: program.content }}
              />
              
              {/* Program Outcomes */}
              <div className="mt-12 p-6 bg-blue-50 rounded-xl">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Program Outcomes</h3>
                <ul className="space-y-3">
                  {program.outcomes.map((outcome, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="h-2 w-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700">{outcome}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Program Details */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Program Details</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Calendar className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">Duration</p>
                    <p className="text-sm text-gray-600">{program.duration}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Target className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">Schedule</p>
                    <p className="text-sm text-gray-600">{program.schedule}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">Location</p>
                    <p className="text-sm text-gray-600">{program.location}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Users className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">Participants</p>
                    <p className="text-sm text-gray-600">{program.participants}</p>
                  </div>
                </div>
                <div className="pt-4 border-t">
                  <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                    Apply Now
                  </button>
                </div>
              </div>
            </div>

            {/* Related Programs */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Related Programs</h3>
              <div className="space-y-4">
                {relatedPrograms.map((relatedProgram) => (
                  <Link
                    key={relatedProgram.id}
                    to={`/program/${relatedProgram.slug}`}
                    className="block group"
                  >
                    <div className="flex space-x-3">
                      <img
                        src={relatedProgram.image}
                        alt={relatedProgram.title}
                        className="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {relatedProgram.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">{relatedProgram.category}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors flex-shrink-0 mt-1" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/programs" className="hover:text-white transition-colors">Education & Literacy</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Workforce Development</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Community Integration</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Women's Empowerment</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Program;
