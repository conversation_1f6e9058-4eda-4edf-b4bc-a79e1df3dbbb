import React from 'react';
import { ExternalLink, Phone, MapPin, Mail, Clock, Users, Heart, BookOpen, Briefcase, Home, Car, GraduationCap, Shield, Building, Facebook, Twitter, Instagram } from 'lucide-react';
import NewsletterSection from './NewsletterSection';
import VolunteerDonateSection from './VolunteerDonateSection';

const johnsonCountyResources = [
  {
    category: "Johnson County Government",
    icon: Building,
    color: "bg-blue-600",
    items: [
      { name: "Johnson County Administration", phone: "(*************", address: "913 S Dubuque St, Iowa City, IA 52240", description: "Main county government office" },
      { name: "Johnson County Health Department", phone: "(*************", address: "855 S Dubuque St, Iowa City, IA 52240", description: "Public health services and programs" },
      { name: "Johnson County Social Services", phone: "(*************", address: "1105 Gilbert Ct, Iowa City, IA 52240", description: "Social services and assistance programs" }
    ]
  },
  {
    category: "Healthcare Services",
    icon: Heart,
    color: "bg-red-500",
    items: [
      { name: "University of Iowa Hospitals", phone: "(*************", address: "200 Hawkins Dr, Iowa City, IA 52242", description: "Major medical center and emergency services" },
      { name: "Free Medical Clinic", phone: "(*************", address: "2271 10th St, Coralville, IA 52241", description: "Free healthcare for uninsured patients" },
      { name: "Community Health Center", phone: "(*************", address: "2105 Broadway St, Iowa City, IA 52240", description: "Affordable primary healthcare" }
    ]
  },
  {
    category: "Housing & Shelter",
    icon: Home,
    color: "bg-green-600",
    items: [
      { name: "Johnson County Housing Trust Fund", phone: "(*************", address: "913 S Dubuque St, Iowa City, IA 52240", description: "Housing assistance and programs" },
      { name: "Shelter House", phone: "(*************", address: "429 Southgate Ave, Iowa City, IA 52240", description: "Emergency shelter and supportive services" },
      { name: "Habitat for Humanity Johnson County", phone: "(*************", address: "2401 Towncrest Dr, Iowa City, IA 52240", description: "Affordable homeownership opportunities" }
    ]
  },
  {
    category: "Employment & Education",
    icon: Briefcase,
    color: "bg-purple-600",
    items: [
      { name: "Iowa Workforce Development - Iowa City", phone: "(*************", address: "1700 S 1st Ave #17A, Iowa City, IA 52240", description: "Job placement and career services" },
      { name: "Kirkwood Community College", phone: "(*************", address: "1816 Lower Muscatine Rd, Iowa City, IA 52240", description: "Career training and ESL classes" },
      { name: "Iowa City Public Library", phone: "(*************", address: "123 S Linn St, Iowa City, IA 52240", description: "Free computer access and literacy programs" }
    ]
  },
  {
    category: "Legal & Immigration Services",
    icon: BookOpen,
    color: "bg-indigo-600",
    items: [
      { name: "Student Legal Services", phone: "(*************", address: "155 IMU, Iowa City, IA 52242", description: "Legal assistance for community members" },
      { name: "Iowa Legal Aid - Johnson County", phone: "(*************", address: "2 S Linn St #802, Iowa City, IA 52240", description: "Free legal services for low-income residents" },
      { name: "Catholic Charities Immigration Services", phone: "(*************", address: "601 E 2nd Ave, Coralville, IA 52241", description: "Immigration legal assistance" }
    ]
  },
  {
    category: "Transportation",
    icon: Car,
    color: "bg-teal-600",
    items: [
      { name: "Iowa City Transit", phone: "(*************", address: "1200 S Riverside Dr, Iowa City, IA 52246", description: "Public bus transportation" },
      { name: "Coralville Transit", phone: "(*************", address: "1512 7th St, Coralville, IA 52241", description: "Local public transportation" },
      { name: "Medical Transport (Johnson County)", phone: "(*************", description: "Transportation for medical appointments" }
    ]
  },
  {
    category: "Community Organizations",
    icon: Users,
    color: "bg-orange-600",
    items: [
      { name: "United Way of Johnson County", phone: "(*************", address: "2301 Ridgeway Dr, Iowa City, IA 52240", description: "Community support and resources" },
      { name: "Crisis Center of Johnson County", phone: "(*************", address: "1121 Gilbert Ct, Iowa City, IA 52240", description: "Crisis intervention and support services" },
      { name: "CommUnity Crisis Services", phone: "(*************", address: "1121 Gilbert Ct, Iowa City, IA 52240", description: "Mental health and crisis support" }
    ]
  },
  {
    category: "Food Assistance",
    icon: Heart,
    color: "bg-pink-600",
    items: [
      { name: "Food Bank of Iowa (Johnson County)", phone: "(*************", address: "2240 Rochester Ave, Iowa City, IA 52245", description: "Food assistance programs" },
      { name: "Salvation Army - Iowa City", phone: "(*************", address: "1116 Gilbert Ct, Iowa City, IA 52240", description: "Food pantry and emergency assistance" },
      { name: "Table to Table", phone: "(*************", address: "2105 Broadway St, Iowa City, IA 52240", description: "Food rescue and distribution" }
    ]
  }
];

const JohnsonCountyResources = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative text-white overflow-hidden py-20">
        <div className="absolute inset-0">
          <img
            src="/US-College-Life-101-Program-2.jpg"
            alt="Johnson County resources"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Johnson County <span className="text-yellow-400">Resources</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
            Local services and support specifically available in Johnson County, Iowa
          </p>
        </div>
      </section>

      {/* County Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">About Johnson County</h2>
          <p className="text-lg text-gray-600 leading-relaxed mb-8">
            Johnson County is home to Iowa City and the University of Iowa, offering a diverse range of services 
            and opportunities for refugees and immigrants. The county provides comprehensive support through 
            government programs, healthcare facilities, educational institutions, and community organizations.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Population</h3>
              <p className="text-gray-600">~151,000 residents</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <GraduationCap className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Education Hub</h3>
              <p className="text-gray-600">University of Iowa</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Heart className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Healthcare</h3>
              <p className="text-gray-600">Major medical center</p>
            </div>
          </div>
        </div>
      </section>

      {/* Resources Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Johnson County Services
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive local resources to support your journey in Johnson County
            </p>
          </div>

          <div className="space-y-12">
            {johnsonCountyResources.map((category, idx) => {
              const IconComponent = category.icon;
              return (
                <div key={idx} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="p-8">
                    <div className="flex items-center mb-8">
                      <div className={`${category.color} p-4 rounded-lg mr-6`}>
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-3xl font-bold text-gray-900">{category.category}</h3>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                      {category.items.map((item, itemIdx) => (
                        <div key={itemIdx} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                          <h4 className="text-xl font-semibold text-gray-900 mb-3">{item.name}</h4>
                          <div className="space-y-2 mb-4">
                            <div className="flex items-start">
                              <Phone className="h-4 w-4 text-blue-600 mr-2 mt-1 flex-shrink-0" />
                              <a href={`tel:${item.phone}`} className="text-blue-600 hover:text-blue-800 font-medium">
                                {item.phone}
                              </a>
                            </div>
                            {item.address && (
                              <div className="flex items-start">
                                <MapPin className="h-4 w-4 text-green-600 mr-2 mt-1 flex-shrink-0" />
                                <span className="text-gray-600 text-sm">{item.address}</span>
                              </div>
                            )}
                          </div>
                          <p className="text-gray-600">{item.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="relative w-full py-20 bg-gray-900 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 w-full h-full">
          <img
            src="/IMG_6267-scaled (1).jpg"
            alt="Group of volunteers holding food donation boxes"
            className="w-full h-full object-cover"
            style={{ filter: 'brightness(0.7)' }}
          />
          <div className="absolute inset-0 bg-black opacity-40"></div>
        </div>
        {/* Overlay Content */}
        <div className="relative z-10 flex flex-col items-center justify-center min-h-[420px] text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-white drop-shadow-lg landing-title">Looking for Assistance?</h2>
          <p className="text-lg md:text-2xl text-white mb-10 max-w-2xl">
            If you need immediate assistance or have questions about any of these resources, please don't hesitate to contact the <span className="font-bold bg-yellow-400 text-deep-blue px-2 rounded">Refugee and Immigrant Association</span>. We're here to help you navigate these services and connect you with the right support.
          </p>
          <div className="flex justify-center">
            <button className="text-white px-6 py-3 rounded-md text-base font-semibold shadow-md transition" style={{ backgroundColor: '#FACC15' }}>
              Contact Us
            </button>
          </div>
        </div>
      </section>
      <NewsletterSection />
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default JohnsonCountyResources;
