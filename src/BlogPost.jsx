import React from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { getCategoryColor, getCategoryColorHex } from './utils';
import { blogPosts, getCategoryCount, getCategories } from './blogData';
import { Calendar } from 'lucide-react';
import NewsletterSection from './NewsletterSection';

const BlogPost = () => {
  const { slug } = useParams();
  
  // Find the specific blog post content (this would normally come from a CMS or API)
  const blogPostsWithContent = [
    {
      id: 1,
      slug: 'empowering-refugee-students-through-college-preparation',
      content: `
        <p>Education is the cornerstone of successful integration for refugee students in their new communities. Our US College Life 101 program has been instrumental in bridging the gap between aspiration and achievement for countless young refugees who dream of pursuing higher education.</p>

        <p>The program addresses the unique challenges that refugee students face when navigating the American higher education system. From understanding application processes to securing financial aid, our comprehensive approach ensures that no student is left behind.</p>

        <h3>Program Highlights</h3>
        <p>Our program includes several key components designed to maximize student success:</p>
        <ul>
          <li>College application assistance and guidance</li>
          <li>Financial aid and scholarship application support</li>
          <li>Academic preparation and study skills development</li>
          <li>Cultural orientation and campus life preparation</li>
          <li>Mentorship programs with successful refugee graduates</li>
        </ul>

        <p>The impact of this program extends far beyond individual success stories. When refugee students succeed in higher education, they become powerful advocates for their communities and serve as role models for younger generations.</p>

        <p>We are proud to report that 95% of our program participants have successfully enrolled in college programs, with many going on to pursue advanced degrees and professional careers that contribute significantly to their communities.</p>
      `
    },
    {
      id: 2,
      slug: 'women-leadership-summit-breaking-barriers-together',
      content: `
        <p>The annual Women Leadership Summit brought together over 200 refugee and immigrant women from across Johnson and Linn Counties to celebrate achievements, share experiences, and build networks for continued empowerment.</p>

        <p>This year's summit focused on "Breaking Barriers Together," highlighting the collective strength that emerges when women support one another in overcoming challenges and pursuing their goals.</p>

        <h3>Key Sessions and Workshops</h3>
        <p>The summit featured a diverse range of sessions designed to inspire and equip participants:</p>
        <ul>
          <li>Leadership skills development workshops</li>
          <li>Entrepreneurship and small business development</li>
          <li>Financial literacy and planning</li>
          <li>Public speaking and advocacy training</li>
          <li>Networking and mentorship opportunities</li>
        </ul>

        <p>One of the most powerful aspects of the summit was the sharing of personal stories. Women from various backgrounds shared their journeys of resilience, highlighting how they overcame obstacles and achieved success in their new communities.</p>

        <p>The summit concluded with the establishment of ongoing support groups and mentorship programs, ensuring that the connections made during the event continue to flourish throughout the year.</p>
      `
    },
    {
      id: 3,
      slug: 'community-recognition-dinner-celebrating-success-stories',
      content: `
        <p>The annual Community Recognition Dinner was a celebration of the remarkable achievements and contributions of refugees and immigrants in our community. The evening honored individuals who have not only succeeded in their personal journeys but have also given back to help others along the way.</p>

        <p>This year's event recognized achievements across multiple categories, from academic excellence and professional success to community service and cultural preservation.</p>

        <h3>Award Categories</h3>
        <p>The evening featured several award categories:</p>
        <ul>
          <li>Academic Excellence Award</li>
          <li>Professional Achievement Award</li>
          <li>Community Service Award</li>
          <li>Cultural Ambassador Award</li>
          <li>Youth Leadership Award</li>
          <li>Lifetime Achievement Award</li>
        </ul>

        <p>Each award recipient shared their story, providing inspiration and hope to other community members who are still navigating their own journeys of integration and success.</p>

        <p>The dinner also served as a fundraising event, with proceeds supporting our ongoing programs and services. The community's generous support ensures that we can continue providing essential services to new arrivals and expanding our impact.</p>

        <p>As we look toward the future, events like these remind us of the incredible strength and resilience within our community, and the importance of celebrating our collective achievements.</p>
      `
    }
  ];

  const post = blogPosts.find(p => p.slug === slug);
  const postContent = blogPostsWithContent.find(p => p.slug === slug);
  
  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Post Not Found</h1>
          <p className="text-gray-600 mb-8">The blog post you're looking for doesn't exist.</p>
          <Link to="/blogs" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  const latestPosts = blogPosts.filter(p => p.slug !== slug).slice(0, 3);
  const categories = getCategories();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section with Breadcrumbs - White background */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <nav className="flex items-center space-x-2 text-sm mb-12">
            <Link to="/" className="text-gray-400 hover:text-gray-600 transition-colors underline">
              Home
            </Link>
            <span className="text-gray-500">›</span>
            <Link to="/blogs" className="text-gray-400 hover:text-gray-600 transition-colors underline">
              Blogs
            </Link>
            <span className="text-gray-500">›</span>
            <span className="text-gray-900">{post.title}</span>
          </nav>

          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Left Content */}
            <div className="space-y-8">
              {/* Title */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900">
                {post.title}
              </h1>
              
              {/* Description */}
              <p className="text-xl text-gray-600 leading-relaxed">
                {post.description}
              </p>
              
              {/* Meta Information Row */}
              <div className="flex items-center space-x-6">
                <span 
                  className="inline-block px-4 py-2 rounded-full text-sm font-bold text-white uppercase tracking-wide"
                  style={{ backgroundColor: getCategoryColorHex(post.category) }}
                >
                  {post.category}
                </span>
                <span className="text-gray-600">by {post.author}</span>
                <span className="text-gray-500 flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {post.date}
                </span>
              </div>
            </div>

            {/* Right Image - Large and prominent */}
            <div className="lg:order-last">
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-[400px] lg:h-[600px] object-cover rounded-3xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Article Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: postContent?.content || '<p>Content coming soon...</p>' }}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Latest Articles */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Latest Articles</h3>
              <div className="space-y-4">
                {latestPosts.map((latestPost) => (
                  <Link
                    key={latestPost.id}
                    to={`/blog/${latestPost.slug}`}
                    className="block group"
                  >
                    <div className="flex space-x-3">
                      <img
                        src={latestPost.image}
                        alt={latestPost.title}
                        className="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {latestPost.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">{latestPost.date}</p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Categories</h3>
              <div className="space-y-3">
                {categories.map((category) => {
                  const articleCount = getCategoryCount(category);
                  return (
                    <Link
                      key={category}
                      to={`/blogs?category=${encodeURIComponent(category)}`}
                      className="flex items-center justify-between p-3 rounded-lg transition-all duration-200 hover:shadow-md group"
                      style={{ backgroundColor: getCategoryColorHex(category) + '15' }}
                    >
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: getCategoryColorHex(category) }}
                        ></div>
                        <span 
                          className="font-medium group-hover:font-semibold transition-all"
                          style={{ color: getCategoryColorHex(category) }}
                        >
                          {category}
                        </span>
                      </div>
                      <span 
                        className="text-sm font-semibold px-2 py-1 rounded-full"
                        style={{ 
                          backgroundColor: getCategoryColorHex(category),
                          color: 'white'
                        }}
                      >
                        {articleCount}
                      </span>
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BlogPost;