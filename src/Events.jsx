import React from 'react';
import VolunteerDonateSection from './VolunteerDonateSection';
import NewsletterSection from './NewsletterSection';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const eventData = [
  {
    title: 'World Refugee Day',
    slug: 'world-refugee-day',
    date: 'June 20, 2024',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    shortDescription: 'Celebrate the strength and resilience of refugees with food, culture, and community.',
    description: `World Refugee Day provides an opportunity for community members to show support for those who were forced to flee, by celebrating their strength, courage, and perseverance. We anticipate guests made up of public officials and civic leaders, members of the refugee and immigrant community, and the general public. There is no fee to attend, but guests will have the opportunity to make a free-will donation. Attendees will enjoy a traditional African dinner and learn more about the refugee and immigrant community, especially the growing number of families from the Democratic Republic of the Congo, Sudan, Burundi, Togo, Liberia, Rwanda, Ethiopia, and other nations.`,
    isUpcoming: true
  },
  {
    title: 'Annual Refugee and Immigrant Recognition Dinner',
    slug: 'recognition-dinner',
    date: 'October 2024',
    image: '/US-College-Life-101-Program-2.jpg',
    shortDescription: 'Meet and learn about the growing refugee community over a traditional African dinner.',
    description: `Public officials, civic leaders, and members of the public are invited to meet with and learn about the growing refugee community in Johnson and Linn counties. The dinner will feature traditional African dishes, followed by a panel discussion. Please contact us for more information and to secure tickets.`,
    isUpcoming: true
  },
  {
    title: 'Night of 1,000 Dinners',
    slug: 'night-of-1000-dinners',
    date: 'March 2024',
    image: '/IMG_9829 (1).jpg',
    shortDescription: "Annual celebration of International Women's Day, focusing on refugee women and children.",
    description: `Night of 1,000 Dinners is an event held annually each March by the Johnson County Chapter of the United Nations Association. It is a community celebration of International Women's Day and focuses on the needs of refugee women and children.`,
    isUpcoming: true
  },
  // Past Events
  {
    title: "International Women's Day",
    slug: 'international-womens-day-2020',
    date: 'March 7, 2020',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    shortDescription: "A community celebration for International Women's Day with a traditional dinner.",
    description: "For the first time in 2020, the Refugee and Immigrant Association hosted a celebration for International Women's Day on March 7th. Individuals came to celebrate with African refugees and immigrant women from our community and were provided a free traditional celebration dinner.",
    isUpcoming: false
  },
  {
    title: 'World Refugee Day',
    slug: 'world-refugee-day-2017',
    date: 'June 17, 2017',
    image: '/US-College-Life-101-Program-2.jpg',
    shortDescription: "Soccer matches, cultural presentations, and keynote address celebrating refugees.",
    description: `Members of the Eastern Iowa refugee community and other community residents celebrated World Refugee Day on Saturday, June 17 at West High School in Iowa City. This international observance recognized the resilience, strength, and contribution of refugees. The day began with soccer matches between teams from several African nations. The afternoon session featured cultural presentations by several groups and individuals and a keynote address by Delphin Kyubwa, Honorary President of the Congolese community in Northern California.`,
    isUpcoming: false
  },
  {
    title: 'WorldCanvass',
    slug: 'worldcanvass-2017',
    date: 'March 8, 2017',
    image: '/IMG_6267-scaled (1).jpg',
    shortDescription: "Panel discussion on immigration and refugee movements in times of crisis.",
    description: `Board member and assistant executive director Boumedien Kasha participated in a panel titled "Immigration/refugee movements in times of crisis." This panel was part of a larger event on the theme "Immigration Then and Now: From German Iowa to Today's Refugees." This event, which ran from 7:30 to 9pm in the Voxman Recital Hall (Voxman Music Building) proved to be an informative look at our present moment. Learn more: https://international.uiowa.edu/news/march-8-worldcanvass-tackles-immigration-then-and-now`,
    isUpcoming: false
  },
  {
    title: "Night of 1,000 Dinners",
    slug: 'night-of-1000-dinners-2017',
    date: 'March 2, 2017',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    shortDescription: "Celebration of International Women's Day and education for refugee girls and boys.",
    description: "We are happy to support the Johnson County Chapter, United Nations Association-USA, in serving as a co-sponsor for the Night of 1,000 Dinners on March 2, 2017, 6pm, at Old Brick in Iowa City. If you enjoy good food and would like to join in a celebration of International Women's Day (including hearing a program on educating refugee girls and boys), please consider attending what is sure to be a wonderful event.",
    isUpcoming: false
  }
];

function getDateParts(dateString) {
  // Try to parse formats like 'June 20, 2024' or 'March 7, 2020' or 'October 2024'
  const date = new Date(dateString);
  if (!isNaN(date)) {
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' }).toUpperCase();
    return { day, month };
  }
  // Fallback for 'October 2024' (no day)
  const [monthStr, dayStr] = dateString.split(' ');
  return { day: dayStr && !isNaN(dayStr) ? dayStr : '', month: monthStr ? monthStr.slice(0,3).toUpperCase() : '' };
}

function EventCard({ event }) {
  const { day, month } = getDateParts(event.date);
  return (
    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group animate-fade-in w-full max-w-md mx-auto">
      <div className="relative h-56 overflow-hidden">
        <img
          src={event.image}
          alt={event.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
        {/* Date Icon */}
        <div className="absolute left-3 bottom-3 bg-yellow-400 text-deep-blue font-bold px-3 py-1 rounded text-xs shadow flex flex-col items-center" style={{fontSize: '0.85rem', minWidth: '40px'}}>
          <span className="block text-base font-bold" style={{fontSize: '1.1rem'}}>{day}</span>
          <span className="uppercase" style={{fontSize: '0.75rem'}}>{month}</span>
        </div>
      </div>
      <div className="p-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-deep-blue transition-colors">
          {event.title}
        </h3>
        <div className="text-sm text-gray-600 mb-2 font-medium">{event.date}</div>
        <p className="text-gray-600 leading-relaxed mb-6">
          {event.shortDescription}
        </p>
        <Link to={`/event/${event.slug}`} className="inline-flex items-center px-6 py-3 bg-deep-blue text-white font-medium rounded-lg hover:bg-bright-blue transition-colors duration-200 group-hover:shadow-md">
          Read more
          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
        </Link>
      </div>
    </div>
  );
}

const Events = () => {
  const upcoming = eventData.filter(e => e.isUpcoming);
  const past = eventData.filter(e => !e.isUpcoming);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section
        className="relative flex flex-col items-center justify-center min-h-[400px] py-20 w-full"
        style={{
          backgroundImage: "url(/RIA-Recognition-Dinner-Pic-4.jpg)",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          paddingTop: '80px',
          paddingBottom: '80px',
        }}
      >
        <div style={{ position: 'absolute', inset: 0, background: 'rgba(0,0,0,0.7)' }}></div>
        <div className="relative z-10 flex flex-col items-center justify-center w-full">
          <h1 className="font-bold mb-6 text-center" style={{ color: '#fff', textTransform: 'capitalize', letterSpacing: 'normal', fontSize: '60px' }}>
            Our <span style={{ color: '#FFC107' }}>Events</span>
          </h1>
          <p className="text-center" style={{ color: '#fff', fontSize: '24px', lineHeight: 1.6, maxWidth: '700px', margin: '0 auto' }}>
            To stay updated with upcoming events please visit our Facebook page.
          </p>
        </div>
      </section>

      {/* Upcoming Events */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Upcoming Events</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {upcoming.map((event, idx) => <EventCard event={event} key={idx} />)}
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Past Events</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {past.map((event, idx) => <EventCard event={event} key={idx} />)}
        </div>
      </div>

      {/* Want to Make a Difference Section */}
      <VolunteerDonateSection />
      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <svg className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <svg className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
                <svg className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                </svg>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Events;
