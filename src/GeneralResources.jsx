import React from 'react';
import { ExternalLink, Phone, MapPin, Mail, Clock, Users, Heart, BookOpen, Briefcase, Home, Car, GraduationCap, Shield, Facebook, Twitter, Instagram } from 'lucide-react';
import NewsletterSection from './NewsletterSection';
import VolunteerDonateSection from './VolunteerDonateSection';

const resources = [
  {
    category: "Emergency Services",
    icon: Shield,
    color: "bg-red-500",
    items: [
      { name: "911 Emergency", phone: "911", description: "Police, Fire, Medical Emergency" },
      { name: "Iowa Crisis & Advocacy Services", phone: "(*************", description: "24/7 crisis support and advocacy" },
      { name: "Foundation 2 Crisis Services", phone: "(*************", description: "Mental health crisis intervention" }
    ]
  },
  {
    category: "Healthcare & Medical",
    icon: Heart,
    color: "bg-blue-500",
    items: [
      { name: "Iowa Department of Public Health", phone: "(*************", description: "State health services and information" },
      { name: "Community Health Centers", phone: "(*************", description: "Affordable healthcare services" },
      { name: "Iowa Medicaid", phone: "(*************", description: "Healthcare coverage assistance" }
    ]
  },
  {
    category: "Legal Services",
    icon: BookOpen,
    color: "bg-purple-500",
    items: [
      { name: "Iowa Legal Aid", phone: "(*************", description: "Free legal assistance for low-income individuals" },
      { name: "Immigration Law Center", phone: "(*************", description: "Immigration legal services" },
      { name: "Pro Bono Legal Services", phone: "(*************", description: "Volunteer attorney services" }
    ]
  },
  {
    category: "Employment & Job Training",
    icon: Briefcase,
    color: "bg-green-500",
    items: [
      { name: "Iowa Workforce Development", phone: "(*************", description: "Job placement and training programs" },
      { name: "Goodwill Industries", phone: "(*************", description: "Job training and placement services" },
      { name: "Kirkwood Community College", phone: "(*************", description: "Career training and education programs" }
    ]
  },
  {
    category: "Housing Assistance",
    icon: Home,
    color: "bg-orange-500",
    items: [
      { name: "Iowa Housing Trust Fund", phone: "(*************", description: "Housing assistance programs" },
      { name: "Housing Choice Voucher Program", phone: "(*************", description: "Rental assistance vouchers" },
      { name: "Habitat for Humanity", phone: "(*************", description: "Homeownership opportunities" }
    ]
  },
  {
    category: "Transportation",
    icon: Car,
    color: "bg-teal-500",
    items: [
      { name: "Iowa DOT", phone: "(*************", description: "Driver's license and vehicle registration" },
      { name: "CRANDIC Public Transit", phone: "(*************", description: "Public transportation services" },
      { name: "Medical Transport Services", phone: "(*************", description: "Medical appointment transportation" }
    ]
  },
  {
    category: "Education & Language",
    icon: GraduationCap,
    color: "bg-indigo-500",
    items: [
      { name: "Iowa Department of Education", phone: "(*************", description: "Educational resources and support" },
      { name: "ESL Classes - Kirkwood", phone: "(*************", description: "English as Second Language classes" },
      { name: "Adult Literacy Programs", phone: "(*************", description: "Adult education and literacy support" }
    ]
  },
  {
    category: "Community Support",
    icon: Users,
    color: "bg-pink-500",
    items: [
      { name: "United Way of East Central Iowa", phone: "(*************", description: "Community resources and support" },
      { name: "Salvation Army", phone: "(*************", description: "Emergency assistance and support services" },
      { name: "Catholic Charities", phone: "(*************", description: "Social services and community support" }
    ]
  }
];

const GeneralResources = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative text-white overflow-hidden py-20">
        <div className="absolute inset-0">
          <img
            src="/RIA-Recognition-Dinner-Pic-4.jpg"
            alt="Community resources"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            General <span className="text-yellow-400">Resources</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
            Essential resources and services to help you navigate life in Iowa
          </p>
        </div>
      </section>

      {/* Resources Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available Resources
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive support services across multiple categories to help you and your family thrive
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {resources.map((category, idx) => {
              const IconComponent = category.icon;
              return (
                <div key={idx} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="p-6">
                    <div className="flex items-center mb-6">
                      <div className={`${category.color} p-3 rounded-lg mr-4`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">{category.category}</h3>
                    </div>
                    <div className="space-y-4">
                      {category.items.map((item, itemIdx) => (
                        <div key={itemIdx} className="border-l-4 border-yellow-400 pl-4 py-2">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{item.name}</h4>
                            <a 
                              href={`tel:${item.phone}`}
                              className="flex items-center text-blue-600 hover:text-blue-800 font-medium"
                            >
                              <Phone className="h-4 w-4 mr-1" />
                              {item.phone}
                            </a>
                          </div>
                          <p className="text-gray-600">{item.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Important Notice */}
      <section className="relative w-full py-20 bg-gray-900 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 w-full h-full">
          <img
            src="/IMG_6267-scaled (1).jpg"
            alt="Group of volunteers holding food donation boxes"
            className="w-full h-full object-cover"
            style={{ filter: 'brightness(0.7)' }}
          />
          <div className="absolute inset-0 bg-black opacity-40"></div>
        </div>
        {/* Overlay Content */}
        <div className="relative z-10 flex flex-col items-center justify-center min-h-[420px] text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-white drop-shadow-lg landing-title">Looking for Assistance?</h2>
          <p className="text-lg md:text-2xl text-white mb-10 max-w-2xl">
            If you need immediate assistance or have questions about any of these resources, please don't hesitate to contact the <span className="font-bold bg-yellow-400 text-deep-blue px-2 rounded">Refugee and Immigrant Association</span>. We're here to help you navigate these services and connect you with the right support.
          </p>
          <div className="flex justify-center">
            <button className="text-white px-6 py-3 rounded-md text-base font-semibold shadow-md transition" style={{ backgroundColor: '#FACC15' }}>
              Contact Us
            </button>
          </div>
        </div>
      </section>
      <NewsletterSection />
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default GeneralResources;
