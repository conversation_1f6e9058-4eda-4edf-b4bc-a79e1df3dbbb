
import React, { useState } from 'react';
import NewsletterSection from './NewsletterSection';
import { <PERSON> } from 'react-router-dom';

const galleryImages = [
  {
    id: 1,
    src: '/US-College-Life-101-Program-2.jpg',
    alt: 'College Life 101 Program',
    category: 'Education',
    title: 'College Life 101 Program'
  },
  {
    id: 2,
    src: '/IMG_9829 (1).jpg',
    alt: 'Women Empowerment Workshop',
    category: 'Empowerment',
    title: 'Women Empowerment Workshop'
  },
  {
    id: 3,
    src: '/RIA-Recognition-Dinner-Pic-4.jpg',
    alt: 'Recognition Dinner Event',
    category: 'Community',
    title: 'Recognition Dinner Event'
  },
  {
    id: 4,
    src: '/IMG_6267-scaled (1).jpg',
    alt: 'Community Volunteer Event',
    category: 'Community',
    title: 'Community Volunteer Event'
  },
  {
    id: 5,
    src: '/US-College-Life-101-Program-2.jpg',
    alt: 'Academic Support Session',
    category: 'Education',
    title: 'Academic Support Session'
  },
  {
    id: 6,
    src: '/IMG_9829 (1).jpg',
    alt: 'Leadership Training',
    category: 'Skills',
    title: 'Leadership Training'
  },
  {
    id: 7,
    src: '/RIA-Recognition-Dinner-Pic-4.jpg',
    alt: 'Community Celebration',
    category: 'Community',
    title: 'Community Celebration'
  },
  {
    id: 8,
    src: '/IMG_6267-scaled (1).jpg',
    alt: 'Food Distribution Drive',
    category: 'Support',
    title: 'Food Distribution Drive'
  },
  {
    id: 9,
    src: '/US-College-Life-101-Program-2.jpg',
    alt: 'Educational Workshop',
    category: 'Education',
    title: 'Educational Workshop'
  },
  {
    id: 10,
    src: '/IMG_9829 (1).jpg',
    alt: 'Women Support Group',
    category: 'Empowerment',
    title: 'Women Support Group'
  },
  {
    id: 11,
    src: '/RIA-Recognition-Dinner-Pic-4.jpg',
    alt: 'Cultural Event',
    category: 'Community',
    title: 'Cultural Event'
  },
  {
    id: 12,
    src: '/IMG_6267-scaled (1).jpg',
    alt: 'Community Outreach',
    category: 'Support',
    title: 'Community Outreach'
  }
];

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedImage, setSelectedImage] = useState(null);

  const categories = ['All', 'Education', 'Empowerment', 'Community', 'Skills', 'Support'];

  const filteredImages = galleryImages.filter(image => 
    selectedCategory === 'All' || image.category === selectedCategory
  );

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative text-white overflow-hidden h-96">
        <div className="absolute inset-0">
          <img
            src="/IMG_6267-scaled (1).jpg"
            alt="Community gallery"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
          <div className="text-left">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Our <span className="text-yellow-400">Gallery</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl">
              Explore moments that capture the heart of our community - from educational programs to cultural celebrations.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Category Filter */}
          <div className="text-center mb-12">
            <div className="inline-flex flex-wrap gap-3 bg-white p-2 rounded-lg shadow-sm">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-md font-semibold transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredImages.map((image) => (
              <div 
                key={image.id} 
                className="group cursor-pointer"
                onClick={() => setSelectedImage(image)}
              >
                <div className="relative overflow-hidden rounded-xl shadow-lg bg-white">
                  <img
                    src={image.src}
                    alt={image.alt}
                    className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-end">
                    <div className="p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                      <div className="text-sm font-semibold mb-1">{image.category}</div>
                      <div className="text-lg font-bold">{image.title}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Load More Images
            </button>
          </div>
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute -top-10 right-0 text-white text-2xl hover:text-gray-300 transition-colors"
            >
              ✕
            </button>
            <img
              src={selectedImage.src}
              alt={selectedImage.alt}
              className="max-w-full max-h-[80vh] object-contain rounded-lg"
            />
            <div className="text-center mt-4 text-white">
              <div className="text-sm text-gray-300 mb-1">{selectedImage.category}</div>
              <div className="text-xl font-bold">{selectedImage.title}</div>
            </div>
          </div>
        </div>
      )}

      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/programs" className="hover:text-white transition-colors">Education & Literacy</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Workforce Development</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Community Integration</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Women's Empowerment</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Gallery;
