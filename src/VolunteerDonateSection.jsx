
import React from 'react';
import { useVolunteerModal } from './contexts/VolunteerModalContext';
import { useDonationModal } from './contexts/DonationModalContext';

const VolunteerDonateSection = () => {
  const { openModal } = useVolunteerModal();
  const { openModal: openDonationModal } = useDonationModal();
  
  return (
    <section className="relative w-full py-20 bg-gray-900 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <img
          src="/IMG_6267-scaled (1).jpg"
          alt="Group of volunteers holding food donation boxes"
          className="w-full h-full object-cover"
          style={{ filter: 'brightness(0.7)' }}
        />
        <div className="absolute inset-0 bg-black opacity-40"></div>
      </div>
      {/* Overlay Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-[420px] text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif text-white drop-shadow-lg landing-title">Want to Get Involved?</h2>
        <p className="text-lg md:text-2xl text-white mb-10 max-w-2xl">Please contact us if you are interested in learning more about the programs and services we offer.</p>
        <div className="flex space-x-6">
          <button
            onClick={openModal}
            className="text-white px-6 py-3 rounded-md text-base font-semibold shadow-md transition hover:opacity-90"
            style={{ backgroundColor: '#00A4F1' }}
          >
            Volunteer With Us
          </button>
          <button 
            onClick={openDonationModal}
            className="text-white px-6 py-3 rounded-md text-base font-semibold shadow-md transition hover:opacity-90" 
            style={{ backgroundColor: '#FACC15' }}
          >
            Support Our Mission
          </button>
        </div>
      </div>
    </section>
  );
};

export default VolunteerDonateSection;
