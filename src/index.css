@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif, Knewave;
    scroll-behavior: smooth;
  }
  
  body {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }

  .landing-title {
    font-family: 'Merriweather', serif;
  }

  /* Blog post prose styles */
  .prose {
    color: #374151;
    max-width: none;
  }
  
  .prose p {
    margin-bottom: 1.25rem;
    line-height: 1.75;
  }
  
  .prose h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-top: 2rem;
    margin-bottom: 1rem;
    line-height: 1.375;
  }
  
  .prose h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    line-height: 1.375;
  }
  
  .prose ul {
    margin-bottom: 1.25rem;
    padding-left: 1.5rem;
  }
  
  .prose li {
    margin-bottom: 0.5rem;
    line-height: 1.75;
  }
  
  .prose strong {
    font-weight: 600;
    color: #111827;
  }
  
  .prose blockquote {
    border-left: 4px solid #3B82F6;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6B7280;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  .animate-zoom {
    animation: zoom 5s infinite;
  }

  @keyframes zoom {
    from {
      transform: scale(1);
    }
    to {
      transform: scale(1.1);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .hero-image-transition {
    transition: opacity 1s ease-in-out;
  }
}