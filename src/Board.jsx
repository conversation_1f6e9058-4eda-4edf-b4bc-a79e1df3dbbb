import React from 'react';
import VolunteerDonateSection from './VolunteerDonateSection';
import { Facebook, Twitter, Instagram } from 'lucide-react';
import Navbar from './Navbar';

const boardMembers = [
  {
    name: '<PERSON>',
    title: 'President',
    image: '/IMG_6267-scaled (1).jpg',
    bio: 'A passionate advocate for refugee families, leading with vision and compassion.'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    title: 'Vice President',
    image: '/IMG_9829 (1).jpg',
    bio: 'Dedicated to empowering newcomers and building inclusive communities.'
  },
  {
    name: '<PERSON><PERSON>',
    title: 'Treasurer',
    image: '/US-College-Life-101-Program-2.jpg',
    bio: 'Ensures financial transparency and supports sustainable growth.'
  },
  {
    name: '<PERSON><PERSON>',
    title: 'Secretary',
    image: '/RIA-Recognition-Dinner-Pic-4.jpg',
    bio: 'Keeps our mission organized and our community connected.'
  },
  {
    name: '<PERSON>',
    title: 'Board Member',
    image: '/job.png',
    bio: 'Brings expertise in workforce development and integration.'
  },
  {
    name: '<PERSON>',
    title: 'Board Member',
    image: '/icons8-family-100.png',
    bio: 'Focuses on family support and youth empowerment.'
  }
];

export default function Board() {
  return (
    <div style={{ fontFamily: 'sans-serif', background: '#fff' }}>
      {/* Use shared Navbar exactly as on the homepage */}
      <Navbar />

      {/* Hero Section with same background image */}
      <section
        className="relative flex flex-col items-center justify-center min-h-[400px] py-20 w-full"
        style={{
          backgroundImage: 'url(/RIA-Recognition-Dinner-Pic-4.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          paddingTop: '80px',
          paddingBottom: '80px',
        }}
      >
        {/* Overlay for readability, but lighter for more image visibility */}
        <div style={{ position: 'absolute', inset: 0, background: 'rgba(0,0,0,0.18)' }}></div>
        <div className="relative z-10 flex flex-col items-center justify-center w-full">
          <h1
            className="font-bold mb-6 text-center"
            style={{ color: '#fff', textTransform: 'capitalize', letterSpacing: 'normal', fontSize: '60px' }}
          >
            Meet <span style={{ color: '#FFC107' }}>The Board</span>
          </h1>
          <p
            className="text-center"
            style={{
              color: '#fff',
              fontSize: '24px',
              lineHeight: 1.6,
              maxWidth: '700px',
              margin: '0 auto',
            }}
          >
            Our dedicated board members bring diverse expertise and unwavering commitment to empowering refugee families and building stronger communities.
          </p>
        </div>
      </section>

      {/* Leadership Section */}
      <section
        className="w-full py-16 px-4 md:px-8"
        style={{ background: '#fff', paddingTop: '60px', paddingBottom: '60px' }}
      >
        <div className="max-w-5xl mx-auto">
          <h2
            className="text-3xl md:text-4xl font-bold text-center mb-4"
            style={{ color: '#003F5E', fontSize: '32px' }}
          >
            Our Leadership Team
          </h2>
          <p
            className="text-base text-center mb-10"
            style={{
              color: '#444',
              fontSize: '16px',
              lineHeight: 1.5,
              maxWidth: '720px',
              margin: '10px auto 30px auto',
            }}
          >
            Meet the visionary leaders who guide the Refugee and Immigrant organization's mission and ensure our programs create lasting impact in the refugee community.
            <br /><br />
            The board meets once a month to plan and implement the activities of the Refugee and Immigrant Association.
          </p>
          <div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8"
            style={{ gap: '24px' }}
          >
            {boardMembers.map((member, idx) => (
              <div
                key={idx}
                className="flex flex-col items-center bg-white rounded-xl shadow-lg overflow-hidden p-6"
                style={{
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  alignItems: 'center',
                  textAlign: 'center',
                }}
              >
                <div
                  className="overflow-hidden mb-4 rounded-xl"
                  style={{ width: '381px', height: '320px', objectFit: 'cover', borderRadius: '16px' }}
                >
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover"
                    style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px' }}
                  />
                </div>
                <div style={{ fontWeight: 700, fontSize: '18px', color: '#003F5E', marginTop: '12px' }}>{member.name}</div>
                <div style={{ color: '#FFC107', fontWeight: 500, fontSize: '15px', marginTop: '4px' }}>{member.title}</div>
                <div style={{ color: '#666', fontSize: '15px', marginTop: '10px' }}>{member.bio}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Want to Make a Difference Section */}
      <VolunteerDonateSection />

      {/* Footer Section */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
              <div className="flex space-x-4">
                <Facebook className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Twitter className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
                <Instagram className="h-6 w-6 text-gray-400 hover:text-yellow-400 cursor-pointer transition-colors" />
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
} 