
import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Calendar, MapPin, Clock, Users, DollarSign, ArrowRight } from 'lucide-react';
import NewsletterSection from './NewsletterSection';

const Event = () => {
  const { slug } = useParams();
  
  // Sample events data with comprehensive content
  const events = [
    {
      id: 1,
      slug: 'world-refugee-day',
      image: '/RIA-Recognition-Dinner-Pic-4.jpg',
      title: 'World Refugee Day',
      category: 'Community',
      description: 'Celebrate the strength and resilience of refugees with food, culture, and community.',
      date: 'June 20, 2024',
      time: '11:00 AM - 6:00 PM',
      location: 'Johnson County Community Center',
      address: '123 Community Drive, Iowa City, IA 52240',
      cost: 'Free',
      capacity: '300+ attendees',
      content: `
        <p>World Refugee Day provides an opportunity for community members to show support for those who were forced to flee, by celebrating their strength, courage, and perseverance. We anticipate guests made up of public officials and civic leaders, members of the refugee and immigrant community, and the general public.</p>

        <p>There is no fee to attend, but guests will have the opportunity to make a free-will donation. Attendees will enjoy a traditional African dinner and learn more about the refugee and immigrant community, especially the growing number of families from the Democratic Republic of the Congo, Sudan, Burundi, Togo, Liberia, Rwanda, Ethiopia, and other nations.</p>

        <h3>Cultural Celebrations</h3>
        <p>The event will feature authentic cultural presentations:</p>
        <ul>
          <li>Traditional music and dance performances</li>
          <li>African cuisine and traditional dishes</li>
          <li>Cultural exhibitions and displays</li>
          <li>Storytelling sessions from community members</li>
          <li>Children's activities and cultural games</li>
          <li>Community resource fair</li>
        </ul>

        <h3>Community Impact</h3>
        <p>This celebration brings together diverse communities to foster understanding, build bridges, and create lasting connections between refugees and long-time residents of Johnson and Linn Counties.</p>

        <p>The event serves as both a celebration and an educational opportunity, helping to dispel myths and build support for refugee integration initiatives.</p>
      `,
      agenda: [
        { time: '11:00 AM', activity: 'Registration and Welcome' },
        { time: '11:30 AM', activity: 'Opening Ceremony and Speeches' },
        { time: '12:00 PM', activity: 'Cultural Dance Performances' },
        { time: '1:00 PM', activity: 'Traditional Lunch Service' },
        { time: '2:30 PM', activity: 'Community Resource Fair' },
        { time: '3:30 PM', activity: 'Children\'s Cultural Activities' },
        { time: '4:30 PM', activity: 'Storytelling Circle' },
        { time: '5:30 PM', activity: 'Closing Remarks and Thanks' }
      ]
    },
    {
      id: 2,
      slug: 'recognition-dinner',
      image: '/US-College-Life-101-Program-2.jpg',
      title: 'Annual Refugee and Immigrant Recognition Dinner',
      category: 'Recognition',
      description: 'Meet and learn about the growing refugee community over a traditional African dinner.',
      date: 'October 15, 2024',
      time: '6:00 PM - 9:00 PM',
      location: 'Iowa City Convention Center',
      address: '456 Convention Way, Iowa City, IA 52240',
      cost: '$25 per person',
      capacity: '150 guests',
      content: `
        <p>Public officials, civic leaders, and members of the public are invited to meet with and learn about the growing refugee community in Johnson and Linn counties. The dinner will feature traditional African dishes, followed by a panel discussion.</p>

        <p>This annual event serves as a platform to recognize the contributions of refugees and immigrants to our community while fostering dialogue and understanding between different cultural groups.</p>

        <h3>Evening Program</h3>
        <p>The evening will include several key components:</p>
        <ul>
          <li>Welcome reception with traditional appetizers</li>
          <li>Authentic African dinner service</li>
          <li>Panel discussion with community leaders</li>
          <li>Recognition awards for outstanding contributions</li>
          <li>Cultural entertainment and performances</li>
          <li>Networking opportunities</li>
        </ul>

        <h3>Community Recognition</h3>
        <p>Special recognition will be given to individuals and organizations who have made significant contributions to refugee resettlement and integration efforts in our area.</p>

        <p>Please contact us for more information and to secure tickets. This event typically sells out, so early registration is recommended.</p>
      `,
      agenda: [
        { time: '6:00 PM', activity: 'Registration and Welcome Reception' },
        { time: '6:30 PM', activity: 'Opening Remarks' },
        { time: '7:00 PM', activity: 'Traditional Dinner Service' },
        { time: '8:00 PM', activity: 'Panel Discussion' },
        { time: '8:30 PM', activity: 'Recognition Awards' },
        { time: '8:45 PM', activity: 'Cultural Entertainment' },
        { time: '9:00 PM', activity: 'Networking and Closing' }
      ]
    },
    {
      id: 3,
      slug: 'night-of-1000-dinners',
      image: '/IMG_9829 (1).jpg',
      title: 'Night of 1,000 Dinners',
      category: 'Women\'s Empowerment',
      description: "Annual celebration of International Women's Day, focusing on refugee women and children.",
      date: 'March 8, 2024',
      time: '6:00 PM - 9:00 PM',
      location: 'Cedar Rapids Community Center',
      address: '789 Women\'s Way, Cedar Rapids, IA 52401',
      cost: '$15 per person',
      capacity: '200 participants',
      content: `
        <p>Night of 1,000 Dinners is an event held annually each March by the Johnson County Chapter of the United Nations Association. It is a community celebration of International Women's Day and focuses on the needs of refugee women and children.</p>

        <p>This inspiring evening brings together women from diverse backgrounds to celebrate achievements, share stories, and discuss the ongoing challenges and opportunities facing refugee women in our community.</p>

        <h3>Program Focus</h3>
        <p>The evening will highlight several important themes:</p>
        <ul>
          <li>Women's empowerment and leadership development</li>
          <li>Educational opportunities for refugee girls and women</li>
          <li>Economic empowerment and entrepreneurship</li>
          <li>Health and wellness initiatives</li>
          <li>Community building and support networks</li>
          <li>Advocacy and policy discussions</li>
        </ul>

        <h3>Featured Speakers</h3>
        <p>The event will feature inspiring speakers including successful refugee women entrepreneurs, community leaders, and advocates who will share their journeys and insights.</p>

        <p>If you enjoy good food and would like to join in a celebration of International Women's Day, please consider attending what is sure to be a wonderful and inspiring event.</p>
      `,
      agenda: [
        { time: '6:00 PM', activity: 'Registration and Welcome' },
        { time: '6:30 PM', activity: 'Opening Ceremony' },
        { time: '7:00 PM', activity: 'Dinner Service' },
        { time: '7:45 PM', activity: 'Keynote Speaker' },
        { time: '8:15 PM', activity: 'Panel Discussion' },
        { time: '8:45 PM', activity: 'Community Recognition' },
        { time: '9:00 PM', activity: 'Networking and Closing' }
      ]
    }
  ];

  const event = events.find(e => e.slug === slug);
  
  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Event Not Found</h1>
          <p className="text-gray-600 mb-8">The event you're looking for doesn't exist.</p>
          <Link to="/events" className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  const upcomingEvents = events.filter(e => e.slug !== slug).slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section - White background like blog posts */}
      <div className="bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Breadcrumbs */}
          <nav className="flex items-center space-x-2 text-sm mb-8">
            <Link to="/" className="text-gray-400 hover:text-gray-600 transition-colors">
              Home
            </Link>
            <span className="text-gray-400">›</span>
            <Link to="/events" className="text-gray-400 hover:text-gray-600 transition-colors">
              Events
            </Link>
            <span className="text-gray-400">›</span>
            <span className="text-gray-600">{event.title}</span>
          </nav>

          {/* Title and Meta */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4">
              {event.category}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {event.title}
            </h1>
            <div className="flex items-center justify-center space-x-6 text-gray-600 mb-8">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>{event.date}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>{event.time}</span>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="mb-12">
            <img
              src={event.image}
              alt={event.title}
              className="w-full h-[400px] object-cover rounded-2xl shadow-lg"
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Event Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: event.content }}
              />
              
              {/* Event Agenda */}
              <div className="mt-12 p-6 bg-green-50 rounded-xl">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Event Agenda</h3>
                <div className="space-y-4">
                  {event.agenda.map((item, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="bg-green-600 text-white text-sm font-bold px-3 py-1 rounded-full flex-shrink-0">
                        {item.time}
                      </div>
                      <p className="text-gray-700 pt-1">{item.activity}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Event Details */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Event Details</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Calendar className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">{event.date}</p>
                    <p className="text-sm text-gray-600">{event.time}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">{event.location}</p>
                    <p className="text-sm text-gray-600">{event.address}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <DollarSign className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">{event.cost}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Users className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <p className="font-semibold text-gray-900">{event.capacity}</p>
                  </div>
                </div>
                <div className="pt-4 border-t">
                  <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                    Register Now
                  </button>
                </div>
              </div>
            </div>

            {/* Upcoming Events */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">More Events</h3>
              <div className="space-y-4">
                {upcomingEvents.map((upcomingEvent) => (
                  <Link
                    key={upcomingEvent.id}
                    to={`/event/${upcomingEvent.slug}`}
                    className="block group"
                  >
                    <div className="flex space-x-3">
                      <img
                        src={upcomingEvent.image}
                        alt={upcomingEvent.title}
                        className="w-16 h-16 object-cover rounded-lg flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {upcomingEvent.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">{upcomingEvent.date}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors flex-shrink-0 mt-1" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/programs" className="hover:text-white transition-colors">Education & Literacy</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Workforce Development</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Community Integration</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Women's Empowerment</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Event;
