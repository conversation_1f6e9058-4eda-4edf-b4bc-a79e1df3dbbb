{"name": "vite-react-jsx-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lovable-tagger": "^1.1.8", "lucide-react": "^0.523.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.4.2"}}